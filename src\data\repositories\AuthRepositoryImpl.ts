import { AuthRepository } from '../../domain/repositories/AuthRepository';
import { AuthUser, LoginCredentials, RegisterData, ResetPasswordData, ChangePasswordData, User } from '../../domain/entities/User';
import { apiClient } from '../datasources/ApiClient';
import { API_ENDPOINTS } from '../../config/ApiConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import Keychain from 'react-native-keychain'; // Temporairement désactivé

export class AuthRepositoryImpl implements AuthRepository {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user_data';

  async login(credentials: LoginCredentials): Promise<AuthUser> {
    try {
      console.log('🔐 Tentative de connexion:', credentials.email);

      // Utiliser l'endpoint avec support automatique de la langue
      const response = await apiClient.postWithLang<any>(API_ENDPOINTS.AUTH.LOGIN, credentials);

      console.log('📥 Réponse API complète:', response);

      // Extraire les données selon la structure de l'API E-Luxe
      const apiData = response.data || response;
      const userData = apiData.user || apiData.data?.user;
      const accessToken = apiData.token || apiData.accessToken || apiData.data?.accessToken;
      const refreshToken = apiData.refreshToken || apiData.data?.refreshToken;
      const expiresAt = apiData.expiresAt || apiData.data?.expiresAt;

      console.log('📊 Données extraites:', { userData, accessToken, refreshToken, expiresAt });

      // Validation des données requises
      if (!accessToken || !refreshToken) {
        throw new Error('Token d\'authentification manquant dans la réponse API');
      }

      // Créer l'objet AuthUser avec validation
      const authUser: AuthUser = {
        user: userData || {
          id: 'temp-id',
          email: credentials.email,
          firstName: 'User',
          lastName: 'Unknown',
        },
        token: accessToken,
        refreshToken: refreshToken,
        expiresAt: expiresAt ? new Date(expiresAt) : new Date(Date.now() + 24 * 60 * 60 * 1000), // 24h par défaut
      };

      console.log('✅ AuthUser créé:', { ...authUser, token: '***', refreshToken: '***' });

      // Store tokens securely
      await this.storeAuthData(authUser);

      return authUser;
    } catch (error: any) {
      console.error('❌ Erreur de connexion complète:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      // Gestion spécifique des erreurs d'authentification
      if (error.response) {
        const status = error.response.status;
        const responseData = error.response.data;

        // Extraire le message d'erreur selon le format E-Luxe
        let errorMessage = 'Erreur de connexion inconnue';

        if (responseData?.data?.errMsg) {
          errorMessage = responseData.data.errMsg;
        } else if (responseData?.message) {
          errorMessage = responseData.message;
        } else if (responseData?.errMsg) {
          errorMessage = responseData.errMsg;
        }

        switch (status) {
          case 400:
            throw new Error(`Données invalides: ${errorMessage}`);
          case 401:
            throw new Error(`Email ou mot de passe incorrect: ${errorMessage}`);
          case 403:
            throw new Error(`Accès refusé: ${errorMessage}`);
          case 404:
            throw new Error(`Service d'authentification non disponible: ${errorMessage}`);
          case 429:
            throw new Error(`Trop de tentatives: ${errorMessage}`);
          case 500:
            throw new Error(`Erreur serveur: ${errorMessage}`);
          default:
            throw new Error(`Erreur ${status}: ${errorMessage}`);
        }
      } else if (error.request) {
        throw new Error('Impossible de contacter le serveur. Vérifiez votre connexion internet');
      } else {
        throw new Error(`Erreur de connexion: ${error.message || 'Erreur inconnue'}`);
      }
    }
  }

  async register(data: RegisterData): Promise<AuthUser> {
    try {
      const response = await apiClient.postWithLang<{
        user: User;
        token: string;
        refreshToken: string;
        expiresAt: string;
      }>(API_ENDPOINTS.AUTH.REGISTER, data);

      const authUser: AuthUser = {
        user: response.data.user,
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        expiresAt: new Date(response.data.expiresAt),
      };

      // Store tokens securely
      await this.storeAuthData(authUser);

      return authUser;
    } catch (error) {
      throw new Error(`Registration failed: ${error}`);
    }
  }

  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiClient.postWithLang(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      // Continue with local logout even if server call fails
      console.warn('Server logout failed:', error);
    } finally {
      // Clear local storage
      await this.clearAuthData();
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthUser> {
    try {
      const response = await apiClient.postWithLang<{
        user: User;
        token: string;
        refreshToken: string;
        expiresAt: string;
      }>(API_ENDPOINTS.AUTH.REFRESH, { refreshToken });

      const authUser: AuthUser = {
        user: response.data.user,
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        expiresAt: new Date(response.data.expiresAt),
      };

      // Update stored tokens
      await this.storeAuthData(authUser);

      return authUser;
    } catch (error) {
      // Clear invalid tokens
      await this.clearAuthData();
      throw new Error(`Token refresh failed: ${error}`);
    }
  }

  async forgotPassword(data: ResetPasswordData): Promise<void> {
    try {
      await apiClient.postWithLang(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, data);
    } catch (error) {
      throw new Error(`Password reset request failed: ${error}`);
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      await apiClient.postWithLang(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
        token,
        password: newPassword,
      });
    } catch (error) {
      throw new Error(`Password reset failed: ${error}`);
    }
  }

  async changePassword(data: ChangePasswordData): Promise<void> {
    try {
      await apiClient.postWithLang(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data);
    } catch (error) {
      throw new Error(`Password change failed: ${error}`);
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      console.log('🔍 Récupération de l\'utilisateur actuel via API...');
      const response = await apiClient.getWithLang<User>(API_ENDPOINTS.AUTH.ME);

      // Extraire les données utilisateur selon la structure de l'API
      const userData = response.data || response;
      console.log('✅ Utilisateur récupéré via API:', userData);

      return userData;
    } catch (error: any) {
      console.error('❌ Erreur lors de la récupération de l\'utilisateur actuel:', error);
      throw new Error(`Failed to get current user: ${error.message || error}`);
    }
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put<User>('/auth/profile', userData);
      
      // Update stored user data
      await AsyncStorage.setItem(this.USER_KEY, JSON.stringify(response.data));
      
      return response.data;
    } catch (error) {
      throw new Error(`Profile update failed: ${error}`);
    }
  }

  async uploadAvatar(imageUri: string): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('avatar', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'avatar.jpg',
      } as any);

      const response = await apiClient.uploadFile<{ avatarUrl: string }>('/auth/avatar', formData);
      return response.data.avatarUrl;
    } catch (error) {
      throw new Error(`Avatar upload failed: ${error}`);
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      // Utilisation temporaire d'AsyncStorage au lieu de Keychain
      return await AsyncStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.warn('Failed to get stored token:', error);
      return null;
    }
  }

  async getStoredUser(): Promise<User | null> {
    try {
      console.log('📱 Récupération de l\'utilisateur stocké...');
      const userJson = await AsyncStorage.getItem(this.USER_KEY);

      if (userJson) {
        const user = JSON.parse(userJson);
        console.log('✅ Utilisateur trouvé en local:', user.email);
        return user;
      }

      console.log('ℹ️  Aucun utilisateur stocké localement');
      return null;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de l\'utilisateur stocké:', error);
      return null;
    }
  }

  async storeToken(token: string): Promise<void> {
    try {
      // Utilisation temporaire d'AsyncStorage au lieu de Keychain
      await AsyncStorage.setItem(this.TOKEN_KEY, token);
    } catch (error) {
      console.warn('Failed to store token:', error);
    }
  }

  async removeToken(): Promise<void> {
    try {
      // Utilisation temporaire d'AsyncStorage au lieu de Keychain
      await AsyncStorage.removeItem(this.TOKEN_KEY);
    } catch (error) {
      console.warn('Failed to remove token:', error);
    }
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      console.log('🔍 Vérification de l\'authentification...');
      const token = await this.getStoredToken();
      const user = await this.getStoredUser();

      const isAuth = !!(token && user);
      console.log(`✅ Statut d'authentification: ${isAuth ? 'Connecté' : 'Non connecté'}`);

      return isAuth;
    } catch (error) {
      console.error('❌ Erreur lors de la vérification d\'authentification:', error);
      return false;
    }
  }

  async validateToken(token: string): Promise<boolean> {
    try {
      await apiClient.get('/auth/validate', {
        headers: { Authorization: `Bearer ${token}` },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  private async storeAuthData(authUser: AuthUser): Promise<void> {
    try {
      console.log('💾 Stockage des données d\'authentification...');

      // Validation des données avant stockage
      const token = authUser.token || '';
      const refreshToken = authUser.refreshToken || '';
      const user = authUser.user || null;

      if (!token || !refreshToken) {
        throw new Error('Token ou refresh token manquant');
      }

      // Utilisation temporaire d'AsyncStorage au lieu de Keychain
      const dataToStore: [string, string][] = [
        [this.TOKEN_KEY, token],
        [this.REFRESH_TOKEN_KEY, refreshToken],
      ];

      // Ajouter les données utilisateur seulement si elles existent
      if (user) {
        dataToStore.push([this.USER_KEY, JSON.stringify(user)]);
      }

      await AsyncStorage.multiSet(dataToStore);
      console.log('✅ Données d\'authentification stockées avec succès');
    } catch (error) {
      console.error('❌ Erreur lors du stockage des données d\'authentification:', error);
      throw error;
    }
  }

  private async clearAuthData(): Promise<void> {
    try {
      console.log('🧹 Nettoyage des données d\'authentification...');

      // Utilisation temporaire d'AsyncStorage au lieu de Keychain
      await AsyncStorage.multiRemove([this.TOKEN_KEY, this.REFRESH_TOKEN_KEY, this.USER_KEY]);

      console.log('✅ Données d\'authentification nettoyées');
    } catch (error) {
      console.error('❌ Erreur lors du nettoyage des données d\'authentification:', error);
    }
  }
}
