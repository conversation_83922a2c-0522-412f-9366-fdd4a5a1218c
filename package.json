{"name": "ELuxeMobileNew", "version": "0.0.1", "private": true, "scripts": {"android": "npx react-native run-android", "ios": "npx react-native run-ios", "lint": "eslint .", "start": "npx react-native start", "test": "jest", "clean": "npx react-native clean", "android-debug": "npx react-native run-android --variant=debug", "android-release": "npx react-native run-android --variant=release"}, "dependencies": {"@os-team/i18next-react-native-language-detector": "^1.1.6", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "i18next": "^22.5.1", "react": "18.2.0", "react-i18next": "^12.3.1", "react-native": "0.72.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.12.1", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-reanimated": "^3.3.0", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.25.0", "react-native-toast-message": "^2.1.6", "react-native-vector-icons": "^10.2.0", "react-redux": "^8.1.2", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.27.6", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}