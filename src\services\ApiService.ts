/**
 * Service API principal pour E-Luxe Mobile
 * Centralise tous les appels API de l'application
 */

import { ApiClient } from '../data/datasources/ApiClient';
import { AuthApiService } from './AuthApiService';
import { EcommerceApiService } from './EcommerceApiService';
import { SettingsService } from './SettingsService';

class ApiService {
  private apiClient: ApiClient;
  private authService: AuthApiService;
  private ecommerceService: EcommerceApiService;
  private settingsService: SettingsService;

  constructor() {
    this.apiClient = new ApiClient();
    this.authService = new AuthApiService();
    this.ecommerceService = new EcommerceApiService();
    this.settingsService = new SettingsService();
  }

  // Auth methods
  get auth() {
    return this.authService;
  }

  // Ecommerce methods
  get ecommerce() {
    return this.ecommerceService;
  }

  // Settings methods
  get settings() {
    return this.settingsService;
  }

  // Direct API client access
  get client() {
    return this.apiClient;
  }

  // User methods (delegated to auth service)
  async login(email: string, password: string) {
    return this.authService.login({ email, password });
  }

  async register(userData: any) {
    return this.authService.register(userData);
  }

  async logout() {
    return this.authService.logout();
  }

  async getCurrentUser() {
    return this.authService.getProfile();
  }

  async updateProfile(userData: any) {
    return this.authService.updateProfile(userData);
  }

  // Product methods (delegated to ecommerce service)
  async getProducts(params?: any) {
    return this.ecommerceService.getProducts(params);
  }

  async getProductById(id: string) {
    return this.ecommerceService.getProductById(id);
  }

  async getCategories() {
    return this.ecommerceService.getCategories();
  }

  async getBestSellers() {
    return this.ecommerceService.getBestSellersOfMonth();
  }

  async getShowingProducts() {
    return this.ecommerceService.getShowingProducts();
  }

  async getShowingCategories() {
    return this.ecommerceService.getShowingCategories();
  }

  // Order methods (delegated to ecommerce service)
  async createOrder(orderData: any) {
    return this.ecommerceService.createOrder(orderData);
  }

  async getOrders(page?: number) {
    return this.ecommerceService.getOrders({ page: page || 1 });
  }

  async getOrderById(id: string) {
    return this.ecommerceService.getOrder(id);
  }

  // Cart methods (delegated to ecommerce service)
  async addToCart(productId: string, quantity: number) {
    return this.ecommerceService.addToCart(productId, quantity);
  }

  async updateCartItem(itemId: string, quantity: number) {
    return this.ecommerceService.updateCartItem(itemId, quantity);
  }

  async removeFromCart(itemId: string) {
    return this.ecommerceService.removeFromCart(itemId);
  }

  async getCart() {
    return this.ecommerceService.getCart();
  }

  // Settings methods (delegated to settings service)
  async getGlobalSettings() {
    return this.settingsService.getGlobalSettings();
  }

  async getStoreCustomizationSettings() {
    return this.settingsService.getStoreCustomizationSettings();
  }

  async getSliderSettings() {
    return this.settingsService.getSliderSettings();
  }

  // Ecommerce settings methods (delegated to ecommerce service)
  async getEcommerceGlobalSettings() {
    return this.ecommerceService.getGlobalSettings();
  }

  async getEcommerceStoreCustomizationSettings() {
    return this.ecommerceService.getStoreCustomizationSettings();
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
