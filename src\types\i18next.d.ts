/**
 * Déclarations TypeScript pour corriger les problèmes de compatibilité i18next
 * Ces déclarations permettent de contourner les erreurs de syntaxe TypeScript
 * dans les fichiers de définition i18next et react-i18next
 */

declare module 'i18next' {
  interface TFunction {
    (key: string, options?: any): string;
    (key: string, defaultValue?: string, options?: any): string;
  }
}

declare module 'react-i18next' {
  export function useTranslation(ns?: string | string[], options?: any): {
    t: (key: string, options?: any) => string;
    i18n: any;
    ready: boolean;
  };
  
  export interface UseTranslationOptions {
    keyPrefix?: string;
  }
  
  export interface UseTranslationResponse<T = any, K = any> {
    t: (key: string, options?: any) => string;
    i18n: any;
    ready: boolean;
  }
}

// Déclarations globales pour éviter les erreurs de syntaxe
declare global {
  type $Dictionary = Record<string, any>;
  type $Tuple<T> = readonly T[];
  type $NoInfer<T> = T;
}

export {};
