import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { getCategoriesAsync } from '../../store/slices/productSlice';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { ELuxeColors } from '../../../theme/colors';
import { TextStyles, FontWeights } from '../../../theme/typography';

type CategoriesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface Category {
  id: string;
  _id?: string;
  name: string | { en: string; fr: string };
  image: string;
  productCount?: number;
  product_count?: number; // Comme l'API backend
  subcategories?: Category[];
}

// Fonction utilitaire pour gérer les traductions multilingues
const getTranslatedText = (text: string | { en: string; fr: string }, defaultLang = 'en'): string => {
  if (typeof text === 'string') {
    return text;
  }
  if (typeof text === 'object' && text !== null) {
    return text[defaultLang] || text.en || text.fr || '';
  }
  return '';
};

const CategoriesScreen: React.FC = () => {
  const navigation = useNavigation<CategoriesScreenNavigationProp>();
  const dispatch = useAppDispatch();
  
  const { categories, isLoading } = useAppSelector((state) => state.product);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);

  // SUPPRIMÉ: Données mockées - utilisation exclusive de l'API
  // Les catégories viennent de Redux store via l'API

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    filterCategories();
  }, [searchQuery, categories]);

  const loadCategories = async () => {
    try {
      console.log('📂 CategoriesScreen - Chargement des catégories...');
      await dispatch(getCategoriesAsync()).unwrap();
      console.log('✅ CategoriesScreen - Catégories chargées avec succès');
    } catch (error) {
      console.error('❌ CategoriesScreen - Erreur lors du chargement des catégories:', error);
    }
  };

  const filterCategories = () => {
    if (!searchQuery.trim()) {
      setFilteredCategories(categories);
      return;
    }

    const filtered = categories.filter(category =>
      getTranslatedText(category.name).toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.subcategories?.some(sub =>
        getTranslatedText(sub.name).toLowerCase().includes(searchQuery.toLowerCase())
      )
    );

    setFilteredCategories(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const handleCategoryPress = (category: Category) => {
    console.log('📂 CategoriesScreen - Category selected:', getTranslatedText(category.name));
    navigation.navigate('Shop', {
      categoryId: category.id || category._id,
      filters: { category: category.id || category._id },
      title: getTranslatedText(category.name),
    });
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
    >
      {/* Gestion d'image EXACTEMENT comme le client web (CategoryTable.tsx) */}
      <FastImage
        source={{
          uri: (item.image && item.image.trim() !== '')
            ? item.image
            : 'https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png'
        }}
        style={styles.categoryImage}
        resizeMode={FastImage.resizeMode.cover}
        onError={() => {
          console.log('⚠️ Erreur chargement image catégorie:', getTranslatedText(item.name));
        }}
      />
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{getTranslatedText(item.name)}</Text>
        <Text style={styles.productCount}>
          {(item.productCount || item.product_count || 0)} products
        </Text>
        
        {item.subcategories && item.subcategories.length > 0 && (
          <View style={styles.subcategoriesContainer}>
            {item.subcategories.slice(0, 3).map((sub, index) => (
              <Text key={sub.id} style={styles.subcategoryText}>
                {getTranslatedText(sub.name)}
                {index < Math.min(item.subcategories!.length, 3) - 1 && ' • '}
              </Text>
            ))}
            {item.subcategories.length > 3 && (
              <Text style={styles.moreText}>+{item.subcategories.length - 3} more</Text>
            )}
          </View>
        )}
      </View>
      <Icon name="chevron-right" size={24} color="#C7C7CC" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search categories..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery('')}
            >
              <Icon name="clear" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories List */}
      <FlatList
        data={filteredCategories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id || item._id || Math.random().toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {filteredCategories.length === 0 && searchQuery.length > 0 && (
        <View style={styles.emptyContainer}>
          <Icon name="search-off" size={64} color="#C7C7CC" />
          <Text style={styles.emptyTitle}>No categories found</Text>
          <Text style={styles.emptySubtitle}>
            Try searching with different keywords
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ELuxeColors.background,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ELuxeColors.white,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.background,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    ...TextStyles.input,
    color: ELuxeColors.textPrimary,
  },
  clearButton: {
    padding: 4,
  },
  listContainer: {
    paddingVertical: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.white,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    shadowColor: ELuxeColors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...TextStyles.h4,
    color: ELuxeColors.textPrimary,
    marginBottom: 4,
  },
  productCount: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    marginBottom: 6,
  },
  subcategoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  subcategoryText: {
    ...TextStyles.caption,
    color: ELuxeColors.primary,
  },
  moreText: {
    ...TextStyles.caption,
    color: ELuxeColors.textTertiary,
    fontStyle: 'italic',
  },
  separator: {
    height: 1,
    backgroundColor: 'transparent',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    ...TextStyles.body,
    color: ELuxeColors.textTertiary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CategoriesScreen;
