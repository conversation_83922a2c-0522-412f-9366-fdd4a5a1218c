export interface Product {
  id: string;
  title: Record<string, string>;
  slug: string;
  description: Record<string, string>;
  type: 'Simple' | 'Grouped' | 'Variable';
  unit: string;
  quantity: number;
  sku: string;
  barcode?: string;
  track_quantity: boolean;
  sell_when_out_of_stock: boolean;
  original_price: number;
  price: number;
  sale_price?: number;
  discount?: number;
  image: string;
  gallery: string[];
  category: string;
  variants: ProductVariant[];
  is_combination: boolean;
  status: 'show' | 'hide';
  related_products: string[];
  upsells: string[];
  cross_sells: string[];
  extras: string[];
  shipping: ShippingInfo;
  translations?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
}

export interface ProductVariant {
  id: string;
  title: string;
  price: number;
  sale_price?: number;
  quantity: number;
  sku: string;
  barcode?: string;
  image?: string;
  attributes: VariantAttribute[];
}

export interface VariantAttribute {
  name: string;
  value: string;
}

export interface ShippingInfo {
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shipping_class?: string;
}

export interface Category {
  id: string;
  name: Record<string, string>;
  description?: Record<string, string>;
  slug: string;
  image?: string;
  parent?: string;
  status: 'show' | 'hide';
  created_at: Date;
  updated_at: Date;
}

export interface ProductFilter {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  search?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'name_asc' | 'name_desc' | 'newest' | 'rating';
  page?: number;
  limit?: number;
}

export interface ProductReview {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title?: string;
  comment: string;
  images?: string[];
  helpful: number;
  verified: boolean;
  created_at: Date;
}

export interface ProductWishlist {
  id: string;
  userId: string;
  productId: string;
  created_at: Date;
}
