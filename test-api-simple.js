/**
 * Test simple de connectivité API
 * Teste si l'API E-Luxe est accessible
 */

const testApiConnectivity = async () => {
  const apiUrl = 'https://api.e-luxe.fr';
  const testEndpoints = [
    '/v1/en/setting/global',
    '/v1/en/setting/store/customization',
    '/v1/en/categories',
    '/v1/en/products',
  ];

  console.log('🔍 Test de connectivité API E-Luxe...\n');
  console.log(`🌐 URL de base: ${apiUrl}\n`);

  for (const endpoint of testEndpoints) {
    const fullUrl = `${apiUrl}${endpoint}`;
    console.log(`📡 Test: ${endpoint}`);
    
    try {
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Client-Type': 'mobile',
          'X-Client-Version': '1.0.0',
        },
        timeout: 10000,
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS: ${response.status} - ${response.statusText}`);
        
        if (data && typeof data === 'object') {
          if (data.status) {
            console.log(`   📊 Status: ${data.status}`);
          }
          if (Array.isArray(data.data)) {
            console.log(`   📊 Array avec ${data.data.length} éléments`);
          } else if (data.data && typeof data.data === 'object') {
            console.log(`   📊 Objet avec clés: ${Object.keys(data.data).join(', ')}`);
          }
        }
      } else {
        console.log(`❌ ERROR: ${response.status} - ${response.statusText}`);
        const errorText = await response.text();
        console.log(`   💥 Error: ${errorText.substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`💥 NETWORK ERROR: ${error.message}`);
    }
    
    console.log(''); // Ligne vide
  }

  console.log('🏁 Test terminé.\n');
  
  // Diagnostic
  console.log('📋 Diagnostic:');
  console.log('1. Si tous les tests échouent: Problème de connectivité réseau');
  console.log('2. Si certains tests réussissent: API partiellement accessible');
  console.log('3. Si tout fonctionne: Problème dans le code React Native');
  console.log('\n🚀 Solutions:');
  console.log('- Vérifier la connexion internet');
  console.log('- Vérifier les permissions réseau de l\'app');
  console.log('- Vérifier les CORS si erreur 403/404');
  console.log('- Redémarrer l\'émulateur/appareil');
};

// Exécuter le test
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testApiConnectivity();
} else {
  // Browser environment
  console.log('Exécutez testApiConnectivity() dans la console pour tester');
}

module.exports = { testApiConnectivity };
