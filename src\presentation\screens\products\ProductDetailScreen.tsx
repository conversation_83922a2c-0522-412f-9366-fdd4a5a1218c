import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  Share,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { addToCartLocal } from '../../store/slices/cartSlice';
import { getProductByIdAsync } from '../../store/slices/productSlice';
import { addToWishlist, removeFromWishlist } from '../../store/slices/wishlistSlice';
import { ELuxeColors } from '../../../theme/colors';
import { TextStyles, FontWeights } from '../../../theme/typography';
import { RootStackParamList } from '../../navigation/AppNavigator';

const { width } = Dimensions.get('window');

type ProductDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ProductDetail'>;
type ProductDetailScreenRouteProp = {
  params: {
    productId: string;
  };
};

const ProductDetailScreen: React.FC = () => {
  const navigation = useNavigation<ProductDetailScreenNavigationProp>();
  const route = useRoute<ProductDetailScreenRouteProp>();
  const dispatch = useAppDispatch();
  
  const { productId } = route.params;
  const { currentProduct, isLoading } = useAppSelector((state) => state.product);
  const { user } = useAppSelector((state) => state.auth);
  const { wishlistItems } = useAppSelector((state) => state.wishlist);

  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState<any>(null);

  // Vérifier si le produit est dans la wishlist
  const isInWishlist = wishlistItems.some(item =>
    item.productId === productId
  );

  // SUPPRIMÉ: Données mockées - utilisation exclusive de l'API
  // Les détails du produit viennent directement de l'API via getProductByIdAsync

  useEffect(() => {
    loadProductDetails();
  }, [productId]);

  const loadProductDetails = async () => {
    try {
      setLoading(true);
      console.log('🔍 Chargement des détails du produit:', productId);

      // Appel API réel pour charger les détails du produit
      const result = await dispatch(getProductByIdAsync(productId));

      if (getProductByIdAsync.fulfilled.match(result)) {
        const productData = result.payload;
        console.log('✅ Détails du produit chargés:', productData);

        // Extraire les données selon la structure de l'API
        const product = productData.data || productData;
        setProduct(product);
      } else {
        throw new Error(result.payload as string);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des détails:', error);

      // Fallback vers données de démonstration si l'API échoue
      const mockProduct = {
        id: productId,
        _id: productId,
        title: 'Premium Wireless Headphones',
        name: { en: 'Premium Wireless Headphones', fr: 'Casque Sans Fil Premium' },
        description: 'High-quality wireless headphones with noise cancellation and premium sound quality.',
        price: 299.99,
        prices: { price: 299.99, original_price: 399.99 },
        variants: [{ price: 299.99, original_price: 399.99 }],
        images: [
          'https://via.placeholder.com/400x400/D4AF37/FFFFFF?text=Product+1',
          'https://via.placeholder.com/400x400/4ECDC4/FFFFFF?text=Product+2',
          'https://via.placeholder.com/400x400/FF6B6B/FFFFFF?text=Product+3',
        ],
        image: 'https://via.placeholder.com/400x400/D4AF37/FFFFFF?text=Product+1',
        brand: 'AudioTech',
        rating: 4.8,
        reviewCount: 156,
        current_stock: 25,
        category: 'Electronics',
        specifications: {
          'Battery Life': '30 hours',
          'Connectivity': 'Bluetooth 5.0',
          'Weight': '250g',
          'Warranty': '2 years',
        },
        features: [
          'Active Noise Cancellation',
          'Wireless Charging Case',
          'Premium Sound Quality',
          'Comfortable Fit',
        ],
      };

      console.log('🔄 Utilisation des données de fallback');
      setProduct(mockProduct);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (!user) {
      Alert.alert('Login Required', 'Please login to add items to cart');
      return;
    }

    if (!product) {
      Alert.alert('Error', 'Product not found');
      return;
    }

    dispatch(addToCartLocal({
      productId: product.id || product._id,
      title: product.title || product.name?.en || product.name,
      image: product.image || product.images?.[0],
      price: product.price || product.prices?.price,
      quantity: quantity,
      variant: selectedVariant,
    }));

    Alert.alert('Success', 'Product added to cart!');
  };

  const handleBuyNow = () => {
    handleAddToCart();
    navigation.navigate('Cart');
  };

  const handleToggleWishlist = () => {
    if (!user) {
      Alert.alert('Login Required', 'Please login to add items to wishlist');
      return;
    }

    if (!product) {
      Alert.alert('Error', 'Product not found');
      return;
    }

    if (isInWishlist) {
      // Retirer de la wishlist
      dispatch(removeFromWishlist(productId));
      Alert.alert('Removed', 'Product removed from wishlist');
    } else {
      // Ajouter à la wishlist
      const wishlistItem = {
        id: Date.now().toString(),
        productId: product.id || product._id,
        title: product.title || product.name?.en || product.name,
        price: product.price || product.prices?.price,
        originalPrice: product.originalPrice,
        image: product.image || product.images?.[0],
        rating: product.rating || 0,
        discount: product.discount,
        inStock: (product.current_stock || product.stock || 0) > 0,
        addedDate: new Date().toISOString(),
        brand: product.brand,
      };

      dispatch(addToWishlist(wishlistItem));
      Alert.alert('Added', 'Product added to wishlist');
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this amazing product: ${product?.title || product?.name || 'E-Luxe Product'}`,
        url: `https://e-luxe.com/products/${productId}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Icon key={i} name="star" size={16} color="#FFD700" />);
    }

    if (hasHalfStar) {
      stars.push(<Icon key="half" name="star-half" size={16} color="#FFD700" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Icon key={`empty-${i}`} name="star-border" size={16} color="#E0E0E0" />);
    }

    return stars;
  };

  // Affichage du loading pendant le chargement
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ELuxeColors.primary} />
          <Text style={styles.loadingText}>Loading product details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Si pas de produit, afficher un message d'erreur
  if (!product) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error" size={48} color="#FF3B30" />
          <Text style={styles.errorTitle}>Product Not Found</Text>
          <Text style={styles.errorMessage}>The product you're looking for doesn't exist or has been removed.</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        <View style={styles.imageContainer}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / width);
              setSelectedImageIndex(index);
            }}
          >
            {(product.images || [product.image]).filter(Boolean).map((image, index) => (
              <FastImage
                key={index}
                source={{ uri: image }}
                style={styles.productImage}
                resizeMode={FastImage.resizeMode.cover}
              />
            ))}
          </ScrollView>

          {/* Image Indicators */}
          <View style={styles.imageIndicators}>
            {(product.images || [product.image]).filter(Boolean).map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  index === selectedImageIndex ? styles.activeIndicator : styles.inactiveIndicator,
                ]}
              />
            ))}
          </View>

          {/* Discount Badge */}
          {product.discount && product.discount > 0 && (
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>-{product.discount}%</Text>
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton} onPress={handleToggleWishlist}>
              <Icon
                name={isInWishlist ? 'favorite' : 'favorite-border'}
                size={24}
                color={isInWishlist ? '#FF3B30' : '#666666'}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <Icon name="share" size={24} color="#666666" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          {/* Title and Brand */}
          <Text style={styles.brand}>{product.brand || 'E-Luxe'}</Text>
          <Text style={styles.title}>{product.title || product.name?.en || product.name}</Text>

          {/* Rating */}
          <View style={styles.ratingContainer}>
            <View style={styles.stars}>{renderStars(product.rating || 0)}</View>
            <Text style={styles.ratingText}>
              {product.rating || 0} ({product.reviewCount || 0} reviews)
            </Text>
          </View>

          {/* Price */}
          <View style={styles.priceContainer}>
            <Text style={styles.price}>${product.price || product.prices?.price}</Text>
            {(product.originalPrice || product.prices?.original_price) && (product.originalPrice || product.prices?.original_price) > (product.price || product.prices?.price) && (
              <Text style={styles.originalPrice}>${product.originalPrice || product.prices?.original_price}</Text>
            )}
          </View>

          {/* Stock Status */}
          <View style={styles.stockContainer}>
            <Icon
              name={(product.current_stock || product.stock || 0) > 0 ? 'check-circle' : 'cancel'}
              size={16}
              color={(product.current_stock || product.stock || 0) > 0 ? '#4CAF50' : '#FF3B30'}
            />
            <Text style={[styles.stockText, { color: (product.current_stock || product.stock || 0) > 0 ? '#4CAF50' : '#FF3B30' }]}>
              {(product.current_stock || product.stock || 0) > 0
                ? `In Stock (${product.current_stock || product.stock} left)`
                : 'Out of Stock'}
            </Text>
          </View>

          {/* Variants */}
          {product.variants && product.variants.map((variant) => (
            <View key={variant.id} style={styles.variantContainer}>
              <Text style={styles.variantTitle}>{variant.name}:</Text>
              <View style={styles.variantOptions}>
                {variant.options.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.variantOption,
                      selectedVariant?.[variant.name] === option && styles.selectedVariantOption,
                    ]}
                    onPress={() => setSelectedVariant({ ...selectedVariant, [variant.name]: option })}
                  >
                    <Text
                      style={[
                        styles.variantOptionText,
                        selectedVariant?.[variant.name] === option && styles.selectedVariantOptionText,
                      ]}
                    >
                      {option}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}

          {/* Quantity */}
          <View style={styles.quantityContainer}>
            <Text style={styles.quantityTitle}>Quantity:</Text>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Icon name="remove" size={20} color="#666666" />
              </TouchableOpacity>
              <Text style={styles.quantityText}>{quantity}</Text>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Icon name="add" size={20} color="#666666" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{product.description || 'No description available.'}</Text>
          </View>

          {/* Features */}
          {product.features && product.features.length > 0 && (
            <View style={styles.featuresContainer}>
              <Text style={styles.sectionTitle}>Key Features</Text>
              {product.features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <Icon name="check" size={16} color="#4CAF50" />
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={[styles.addToCartButton, (product.current_stock || product.stock || 0) <= 0 && styles.disabledButton]}
          onPress={handleAddToCart}
          disabled={(product.current_stock || product.stock || 0) <= 0}
        >
          <Icon name="add-shopping-cart" size={20} color="#FFFFFF" />
          <Text style={styles.addToCartText}>Add to Cart</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.buyNowButton, (product.current_stock || product.stock || 0) <= 0 && styles.disabledButton]}
          onPress={handleBuyNow}
          disabled={(product.current_stock || product.stock || 0) <= 0}
        >
          <Text style={styles.buyNowText}>Buy Now</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  productImage: {
    width: width,
    height: 300,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: ELuxeColors.primary, // Or E-Luxe au lieu du bleu
  },
  inactiveIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  discountBadge: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  actionButtons: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'column',
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  productInfo: {
    padding: 16,
  },
  brand: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  title: {
    ...TextStyles.h2,
    color: ELuxeColors.textPrimary,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 8,
  },
  ratingText: {
    fontSize: 14,
    color: '#666666',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  price: {
    ...TextStyles.priceLarge,
    color: ELuxeColors.primary,
    marginRight: 12,
  },
  originalPrice: {
    fontSize: 18,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  stockText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  variantContainer: {
    marginBottom: 20,
  },
  variantTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  variantOption: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedVariantOption: {
    borderColor: ELuxeColors.primary,
    backgroundColor: ELuxeColors.primary,
  },
  variantOptionText: {
    fontSize: 14,
    color: '#000000',
  },
  selectedVariantOptionText: {
    color: '#FFFFFF',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  quantityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginRight: 16,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
  },
  quantityButton: {
    padding: 8,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    paddingHorizontal: 16,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#000000',
    marginLeft: 8,
  },
  bottomActions: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    backgroundColor: '#FFFFFF',
  },
  addToCartButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ELuxeColors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    marginRight: 8,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  buyNowButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF3B30',
    borderRadius: 12,
    paddingVertical: 16,
    marginLeft: 8,
  },
  buyNowText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: ELuxeColors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProductDetailScreen;
