/**
 * Composant Logo réutilisable qui utilise le logo depuis l'API
 * Exactement comme le client web E-Luxe
 */

import React from 'react';
import { View, Image, Text, StyleSheet, ViewStyle, ImageStyle, TextStyle } from 'react-native';
import { useAppSelector } from '../../store/store';
import { selectLogo, selectGlobalSetting } from '../../store/slices/settingsSlice';
import { ELuxeColors } from '../../../theme/colors';
import { TextStyles, FontWeights } from '../../../theme/typography';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'light' | 'dark';
  showText?: boolean;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
  textStyle?: TextStyle;
}

const Logo: React.FC<LogoProps> = ({
  size = 'medium',
  variant = 'dark',
  showText = true,
  style,
  imageStyle,
  textStyle,
}) => {
  const { storeCustomizationSetting } = useAppSelector((state: any) => state.setting);
  const globalSetting = useAppSelector(selectGlobalSetting);
  const logoFromSelector = useAppSelector(selectLogo);

  // Logo priorité: navbar.logo > logo > fallback (comme le client web)
  const logoUrl = storeCustomizationSetting?.navbar?.logo || 
                  storeCustomizationSetting?.logo || 
                  logoFromSelector || 
                  '';

  const shopName = globalSetting?.shop_name || 'E-Luxe';

  // Tailles selon la prop size
  const sizes = {
    small: { width: 60, height: 30, fontSize: 16 },
    medium: { width: 120, height: 60, fontSize: 20 },
    large: { width: 180, height: 90, fontSize: 24 },
  };

  const currentSize = sizes[size];

  // Couleurs selon la variante
  const colors = {
    light: {
      text: ELuxeColors.white,
      placeholder: 'rgba(255, 255, 255, 0.2)',
      placeholderText: ELuxeColors.white,
    },
    dark: {
      text: ELuxeColors.textPrimary,
      placeholder: ELuxeColors.background,
      placeholderText: ELuxeColors.primary,
    },
  };

  const currentColors = colors[variant];

  const renderLogo = () => {
    if (logoUrl && logoUrl.trim() !== '') {
      return (
        <Image
          source={{ uri: logoUrl }}
          style={[
            styles.logoImage,
            {
              width: currentSize.width,
              height: currentSize.height,
            },
            imageStyle,
          ]}
          resizeMode="contain"
        />
      );
    }

    // Placeholder si pas de logo
    return (
      <View
        style={[
          styles.logoPlaceholder,
          {
            width: currentSize.height,
            height: currentSize.height,
            borderRadius: currentSize.height / 2,
            backgroundColor: currentColors.placeholder,
          },
        ]}
      >
        <Text
          style={[
            styles.logoPlaceholderText,
            {
              fontSize: currentSize.fontSize,
              color: currentColors.placeholderText,
            },
          ]}
        >
          {shopName.substring(0, 2).toUpperCase()}
        </Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {renderLogo()}
      {showText && (
        <Text
          style={[
            styles.logoText,
            {
              fontSize: currentSize.fontSize,
              color: currentColors.text,
            },
            textStyle,
          ]}
        >
          {shopName}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoImage: {
    // Dimensions dynamiques selon la prop size
  },
  logoPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  logoPlaceholderText: {
    fontWeight: FontWeights.bold,
    textAlign: 'center',
  },
  logoText: {
    fontWeight: FontWeights.semiBold,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default Logo;
