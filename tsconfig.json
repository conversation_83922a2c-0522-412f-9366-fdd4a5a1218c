{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"target": "es2017", "lib": ["es2017", "es6", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["presentation/components/*"], "@/screens/*": ["presentation/screens/*"], "@/navigation/*": ["presentation/navigation/*"], "@/hooks/*": ["presentation/hooks/*"], "@/store/*": ["presentation/store/*"], "@/domain/*": ["domain/*"], "@/data/*": ["data/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/constants/*": ["constants/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios"]}