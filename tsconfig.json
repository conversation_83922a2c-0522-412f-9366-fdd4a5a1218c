{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"target": "es2020", "lib": ["es2020", "es6", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "skipDefaultLibCheck": true, "typeRoots": ["./src/types", "./node_modules/@types"], "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["presentation/components/*"], "@/screens/*": ["presentation/screens/*"], "@/navigation/*": ["presentation/navigation/*"], "@/hooks/*": ["presentation/hooks/*"], "@/store/*": ["presentation/store/*"], "@/domain/*": ["domain/*"], "@/data/*": ["data/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/constants/*": ["constants/*"]}}, "include": ["src/**/*", "src/types/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios", "node_modules/i18next/typescript/**/*", "node_modules/react-i18next/**/*"]}