/**
 * Charte Graphique E-Luxe Mobile
 * Synchronisée avec le client web E-Luxe
 * Couleurs et thème luxueux
 */

export const ELuxeColors = {
  // Couleurs principales (synchronisées avec le web)
  primary: '#D4AF37',           // --tp-text-primary (Or luxueux)
  primaryHover: '#C6A020',      // --tp-text-primary-hover
  primaryLight: '#D4AF3780',    // --tp-text-2 (avec transparence)
  primaryBackground: '#faf6e9', // --tp-text-1

  // Couleurs secondaires
  secondary: '#C6A020',         // Couleur secondaire pour gradients

  // Couleurs de base
  white: '#FFFFFF',             // --tp-common-white
  black: '#000000',             // --tp-common-black
  yellow: '#FFCD00',            // --tp-common-yellow

  // Textes
  textPrimary: '#040404',       // --tp-text-body
  textSecondary: '#777777',     // --tp-text-secondary
  textTertiary: '#ADADAD',      // --tp-text-tertiary
  textMuted: '#999999',         // --tp-text-5
  textLight: '#A0A0A0',         // --tp-text-4

  // Couleurs d'arrière-plan
  background: '#F8F9FA',        // Arrière-plan principal
  backgroundAlt: '#FFFFFF',     // Arrière-plan alternatif

  // Gris (palette complète)
  grey1: '#A5A5A6',             // --tp-grey-1
  grey2: '#F3F4F7',             // --tp-grey-2
  grey3: '#C8C8C8',             // --tp-grey-3
  grey4: '#E6DFD6',             // --tp-grey-4
  grey5: '#FFF3F6',             // --tp-grey-5
  grey6: '#CFCFCF',             // --tp-grey-6
  grey7: '#D3D3D3',             // --tp-grey-7
  grey8: '#F6F6F6',             // --tp-grey-8
  grey9: '#999999',             // --tp-grey-9

  // Thèmes (couleurs d'accent) - CHARTE GRAPHIQUE E-LUXE
  theme1: '#D4AF37',            // --tp-theme-1 (Or E-Luxe au lieu du bleu)
  theme2: '#F3EEE7',            // --tp-theme-2
  theme3: '#F7F7F7',            // --tp-theme-3
  theme4: '#77977B',            // --tp-theme-4 (Vert)
  theme5: '#8E8B74',            // --tp-theme-5
  theme6: '#F8F8F8',            // --tp-theme-6
  theme7: '#EDEDED',            // --tp-theme-7
  theme8: '#F4F1EC',            // --tp-theme-8
  theme9: '#83B735',            // --tp-theme-9 (Vert vif)
  theme10: '#F3F4F7',           // --tp-theme-10
  theme11: '#F3F7F7',           // --tp-theme-11
  theme12: '#F0E2DD',           // --tp-theme-12
  theme13: '#F7EFEC',           // --tp-theme-13
  theme14: '#F58448',           // --tp-theme-14 (Orange)

  // Bordures
  border: '#E7E7E7',            // Bordure principale
  border1: '#E7E7E7',           // --tp-border-1
  border2: '#F6F6F6',           // --tp-border-2
  border3: '#ECF0EF',           // --tp-border-3
  border4: '#E0E0E0',           // --tp-border-4
  border5: '#282828',           // --tp-border-5
  border6: '#E4E4E4',           // --tp-border-6

  // États (succès, erreur, warning, info) - CHARTE GRAPHIQUE E-LUXE
  success: '#10B981',           // Vert succès
  successLight: '#D1FAE5',      // Vert succès clair
  error: '#EF4444',             // Rouge erreur
  errorLight: '#FEE2E2',        // Rouge erreur clair
  warning: '#F59E0B',           // Orange warning
  warningLight: '#FEF3C7',      // Orange warning clair
  info: '#D4AF37',              // Or E-Luxe au lieu du bleu
  infoLight: '#D4AF3720',       // Or E-Luxe clair au lieu du bleu

  // Gradients luxueux (inspirés du web) - CHARTE GRAPHIQUE E-LUXE
  gradients: {
    primary: ['#D4AF37', '#C6A020'],           // Or principal
    luxury: ['#D4AF37', '#F4F1EC', '#D4AF37'], // Gradient luxueux
    dark: ['#000000', '#282828'],              // Gradient sombre
    light: ['#FFFFFF', '#F8F8F8'],             // Gradient clair
    accent: ['#D4AF37', '#C6A020'],            // Or E-Luxe au lieu du bleu
    green: ['#83B735', '#77977B'],             // Gradient vert
    sunset: ['#F58448', '#D4AF37'],            // Gradient coucher de soleil
  },

  // Transparences
  opacity: {
    light: 0.1,
    medium: 0.3,
    heavy: 0.5,
    strong: 0.7,
    veryStrong: 0.9,
  },
};

// Couleurs spécifiques aux composants
export const ComponentColors = {
  // Navigation
  tabBar: {
    active: ELuxeColors.primary,
    inactive: ELuxeColors.textTertiary,
    background: ELuxeColors.white,
    border: ELuxeColors.border1,
  },

  // Boutons (UNIFORMISÉS dans toute l'application)
  button: {
    primary: {
      background: ELuxeColors.primary, // #D4AF37 (or) pour tous les boutons principaux
      text: ELuxeColors.black, // Noir sur or pour contraste maximal
      border: ELuxeColors.primary,
    },
    secondary: {
      background: ELuxeColors.white, // Blanc pour boutons secondaires
      text: ELuxeColors.primary, // Or sur blanc pour boutons secondaires
      border: ELuxeColors.primary,
    },
    outline: {
      background: 'transparent',
      text: ELuxeColors.primary,
      border: ELuxeColors.primary,
    },
    danger: {
      background: '#FF3B30',
      text: ELuxeColors.white,
      border: '#FF3B30',
    },
    success: {
      background: '#34C759',
      text: ELuxeColors.white,
      border: '#34C759',
    },
    disabled: {
      background: ELuxeColors.grey3,
      text: ELuxeColors.textTertiary,
      border: ELuxeColors.grey3,
    },
  },

  // Cartes produits
  productCard: {
    background: ELuxeColors.white,
    border: ELuxeColors.border1,
    shadow: ELuxeColors.black + '10', // 10% opacity
    discount: ELuxeColors.error,
    rating: ELuxeColors.theme14, // Orange
  },

  // Inputs
  input: {
    background: ELuxeColors.white,
    border: ELuxeColors.border1,
    borderFocus: ELuxeColors.primary,
    text: ELuxeColors.textPrimary,
    placeholder: ELuxeColors.textTertiary,
  },

  // Header
  header: {
    background: ELuxeColors.white,
    text: ELuxeColors.textPrimary,
    border: ELuxeColors.border1,
    shadow: ELuxeColors.black + '08', // 8% opacity
  },

  // Écrans
  screen: {
    background: ELuxeColors.grey2, // #F3F4F7
    backgroundAlt: ELuxeColors.white,
  },
};

// Thème sombre (optionnel pour le futur)
export const DarkTheme = {
  primary: ELuxeColors.primary,
  background: ELuxeColors.black,
  surface: '#1A1A1A',
  text: ELuxeColors.white,
  textSecondary: ELuxeColors.grey3,
  border: ELuxeColors.border5,
};

export default ELuxeColors;
