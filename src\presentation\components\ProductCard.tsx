import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  ViewStyle,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppSelector } from '../store/store';
import { getProductTitle, getProductImage, formatPriceWithCurrency, getProductPrice, getProductOriginalPrice, isPromotional } from '../../utils/translationUtils';
import { ELuxeColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';

interface Product {
  id: string;
  title: string;
  price: number;
  image: string;
  rating?: number;
  discount?: number;
  originalPrice?: number;
}

// Interface Product locale compatible
interface ProductLocal {
  id: string;
  _id?: string;
  name?: string;
  title?: string | Record<string, string>;
  price: number;
  originalPrice?: number;
  image?: string;
  images?: string[];
  rating?: number;
  reviewCount?: number;
  discount?: number;
  brand?: string;
  category?: string;
  variants?: any[];
  inStock?: boolean;
  stock?: number;
}

interface ProductCardProps {
  product: ProductLocal;
  onPress: () => void;
  onAddToCart?: () => void;
  onToggleWishlist?: () => void;
  isInWishlist?: boolean;
  showAddToCart?: boolean;
  style?: ViewStyle;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false,
  style,
  showAddToCart = true,
}) => {
  // Vérifier si le produit est dans la wishlist via le store Redux
  const wishlistItems = useAppSelector((state) => state.wishlist?.wishlistItems || []);
  const isProductInWishlist = wishlistItems.some(item =>
    item.productId === (product.id || product._id)
  );

  // Utiliser l'état du store plutôt que la prop
  const actualIsInWishlist = isProductInWishlist || isInWishlist;

  const formatPrice = (price: number) => {
    return `$${price.toFixed(2)}`;
  };

  const renderRating = () => {
    if (!product.rating) return null;

    const stars = [];
    const fullStars = Math.floor(product.rating);
    const hasHalfStar = product.rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Icon key={i} name="star" size={12} color="#FFD700" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Icon key="half" name="star-half" size={12} color="#FFD700" />
      );
    }

    const emptyStars = 5 - Math.ceil(product.rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Icon key={`empty-${i}`} name="star-border" size={12} color="#E0E0E0" />
      );
    }

    return (
      <View style={styles.ratingContainer}>
        <View style={styles.stars}>{stars}</View>
        <Text style={styles.ratingText}>
          {product.rating} ({product.reviewCount || 0} reviews)
        </Text>
      </View>
    );
  };

  const renderVariants = () => {
    if (!product.variants || product.variants.length === 0) return null;

    return (
      <View style={styles.variantsContainer}>
        <Text style={styles.variantsLabel}>Colors:</Text>
        <View style={styles.variantColors}>
          {product.variants.slice(0, 3).map((variant, index) => (
            <View
              key={index}
              style={[
                styles.variantColor,
                { backgroundColor: variant.color || '#E0E0E0' }
              ]}
            />
          ))}
          {product.variants.length > 3 && (
            <Text style={styles.moreVariants}>+{product.variants.length - 3}</Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
      {/* Image Container */}
      <View style={styles.imageContainer}>
        <FastImage
          source={{
            uri: getProductImage(product) || 'https://via.placeholder.com/300x300/D4AF37/FFFFFF?text=E-Luxe'
          }}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
          onError={() => {
            console.log('⚠️ Erreur chargement image produit:', product?.title);
          }}
        />
        
        {/* Discount Badge */}
        {product.discount && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>-{product.discount}%</Text>
          </View>
        )}

        {/* Wishlist Button */}
        {onToggleWishlist && (
          <TouchableOpacity
            style={styles.wishlistButton}
            onPress={onToggleWishlist}
          >
            <Icon
              name={actualIsInWishlist ? 'favorite' : 'favorite-border'}
              size={20}
              color={actualIsInWishlist ? '#FF3B30' : '#666666'}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Brand */}
        {product.brand && (
          <Text style={styles.brand}>{product.brand}</Text>
        )}

        {/* Title */}
        <Text style={styles.title} numberOfLines={2}>
          {getProductTitle(product)}
        </Text>

        {/* Rating */}
        {renderRating()}

        {/* Variants */}
        {renderVariants()}

        {/* Price Container - EXACTEMENT comme le client web */}
        <View style={styles.priceContainer}>
          {isPromotional(product) ? (
            <>
              {/* Prix en promotion (prix réduit) */}
              <Text style={styles.price}>
                {formatPriceWithCurrency(getProductPrice(product))}
              </Text>
              {/* Prix original barré */}
              <Text style={styles.originalPrice}>
                {formatPriceWithCurrency(getProductOriginalPrice(product))}
              </Text>
            </>
          ) : (
            /* Prix normal (pas de promotion) */
            <Text style={styles.price}>
              {formatPriceWithCurrency(getProductOriginalPrice(product))}
            </Text>
          )}
        </View>

        {/* Add to Cart Button */}
        {showAddToCart && onAddToCart && (
          <TouchableOpacity style={styles.addToCartButton} onPress={onAddToCart}>
            <Icon name="add-shopping-cart" size={16} color="#FFFFFF" />
            <Text style={styles.addToCartText}>Add to Cart</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 160,
    backgroundColor: ELuxeColors.white, // Couleur E-Luxe
    borderRadius: 12,
    shadowColor: ELuxeColors.black, // Couleur E-Luxe
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: ELuxeColors.border1, // Bordure E-Luxe pour définir les cartes
  },
  imageContainer: {
    position: 'relative',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FF3B30',
    borderRadius: 6,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  wishlistButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  content: {
    padding: 12,
  },
  brand: {
    fontSize: 11,
    color: ELuxeColors.textSecondary,
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  title: {
    ...TextStyles.productTitle, // Nouveau style optimisé
    color: ELuxeColors.textPrimary, // Couleur E-Luxe
    marginBottom: 4,
    lineHeight: 18,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 4,
  },
  ratingText: {
    fontSize: 9,
    color: ELuxeColors.textSecondary, // Couleur E-Luxe
  },
  variantsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  variantsLabel: {
    fontSize: 9,
    color: ELuxeColors.textSecondary,
    marginRight: 4,
  },
  variantColors: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  variantColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 3,
    borderWidth: 1,
    borderColor: ELuxeColors.border2,
  },
  moreVariants: {
    fontSize: 8,
    color: ELuxeColors.textSecondary,
    marginLeft: 2,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
    color: ELuxeColors.primary, // Couleur or E-Luxe pour le prix
    marginRight: 6,
  },
  originalPrice: {
    fontSize: 12,
    color: ELuxeColors.textTertiary, // Couleur E-Luxe
    textDecorationLine: 'line-through',
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ELuxeColors.primary, // Couleur or E-Luxe
    borderRadius: 8,
    paddingVertical: 6,
    paddingHorizontal: 8,
  },
  addToCartText: {
    color: ELuxeColors.white, // Couleur E-Luxe
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default ProductCard;
