export interface Order {
  id: string;
  order_number: number;
  user: string;
  vendor?: string;
  cart: CartItem[];
  user_info: UserInfo;
  sub_total: number;
  shipping_cost: number;
  tax_price: number;
  discount: number;
  total: number;
  total_price: number;
  delivery_note?: string;
  shipping_option?: string;
  card_info?: PaymentCardInfo;
  invoice?: string;
  status: OrderStatus;
  is_paid: boolean;
  paid_at?: Date;
  is_delivered: boolean;
  delivered_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export type OrderStatus = 'Pending' | 'Processing' | 'Delivered' | 'Cancel';

export interface CartItem {
  id: string;
  productId: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
  variant?: ProductVariantSelection;
  extras?: ExtraItem[];
  total: number;
}

export interface ProductVariantSelection {
  id: string;
  attributes: Record<string, string>;
  price: number;
  sku: string;
}

export interface ExtraItem {
  id: string;
  title: string;
  price: number;
  quantity: number;
}

export interface UserInfo {
  name: string;
  email: string;
  contact: string;
  address: string;
  city: string;
  country: string;
  zipCode: string;
}

export interface PaymentCardInfo {
  last4: string;
  brand: string;
  exp_month: number;
  exp_year: number;
}

export interface ShippingAddress {
  id: string;
  userId: string;
  name: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  isDefault: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  discount: number;
  total: number;
  coupon?: AppliedCoupon;
}

export interface AppliedCoupon {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
}

export interface Coupon {
  id: string;
  code: string;
  description: string;
  type: 'percentage' | 'fixed';
  value: number;
  minimum_amount?: number;
  maximum_discount?: number;
  usage_limit?: number;
  used_count: number;
  expires_at?: Date;
  status: 'active' | 'inactive';
  created_at: Date;
}

export interface OrderTracking {
  orderId: string;
  status: OrderStatus;
  tracking_number?: string;
  carrier?: string;
  estimated_delivery?: Date;
  updates: TrackingUpdate[];
}

export interface TrackingUpdate {
  status: string;
  description: string;
  location?: string;
  timestamp: Date;
}
