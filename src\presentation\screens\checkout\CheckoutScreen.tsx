import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { clearCartLocal } from '../../store/slices/cartSlice';
import { createOrderAsync } from '../../store/slices/orderSlice';
import { RootStackParamList } from '../../navigation/AppNavigator';

type CheckoutScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Checkout'>;

interface ShippingAddress {
  id: string;
  name: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  name: string;
  details: string;
  isDefault: boolean;
}

const CheckoutScreen: React.FC = () => {
  const navigation = useNavigation<CheckoutScreenNavigationProp>();
  const dispatch = useAppDispatch();
  
  const { items, subtotal, shipping, tax, discount, total } = useAppSelector((state) => state.cart);
  const { user } = useAppSelector((state) => state.auth);
  
  const [selectedAddress, setSelectedAddress] = useState<ShippingAddress | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<PaymentMethod | null>(null);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // SUPPRIMÉ: Données mockées - utilisation exclusive de l'API
  // Les adresses et méthodes de paiement viennent de l'API utilisateur

  React.useEffect(() => {
    // Set default selections
    // TODO: Load real addresses and payment methods from API
    const defaultAddress = null;
    const defaultPayment = null;
    
    if (defaultAddress) setSelectedAddress(defaultAddress);
    if (defaultPayment) setSelectedPayment(defaultPayment);
  }, []);

  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      Alert.alert('Missing Information', 'Please select a shipping address');
      return;
    }

    if (!selectedPayment) {
      Alert.alert('Missing Information', 'Please select a payment method');
      return;
    }

    if (items.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty');
      return;
    }

    setIsProcessing(true);

    try {
      // Create order
      const orderData = {
        user: user?.id || 'guest',
        cart: items,
        user_info: {
          name: selectedAddress.name,
          email: user?.email || '',
          contact: selectedAddress.phone,
          address: selectedAddress.address,
          city: selectedAddress.city,
          country: 'US',
          zipCode: selectedAddress.zipCode,
        },
        sub_total: subtotal,
        shipping_cost: shipping,
        tax_price: tax,
        discount: discount,
        total: total,
        total_price: total,
        status: 'Pending' as const,
        is_paid: false,
        is_delivered: false,
        shipping_option: 'standard',
        card_info: selectedPayment.type === 'card' ? {
          last4: selectedPayment.details.slice(-4),
          brand: selectedPayment.name.split(' ')[0],
          exp_month: 12,
          exp_year: 2025,
        } : undefined,
      };

      await dispatch(createOrderAsync(orderData)).unwrap();
      
      // Clear cart
      dispatch(clearCartLocal());
      
      // Show success and navigate
      Alert.alert(
        'Order Placed Successfully!',
        'Your order has been placed and you will receive a confirmation email shortly.',
        [
          {
            text: 'View Orders',
            onPress: () => navigation.navigate('Orders' as any),
          },
          {
            text: 'Continue Shopping',
            onPress: () => navigation.navigate('Main'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Order Failed', 'Failed to place order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getPaymentIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return 'credit-card';
      case 'paypal':
        return 'account-balance-wallet';
      case 'apple_pay':
        return 'phone-iphone';
      case 'google_pay':
        return 'phone-android';
      default:
        return 'payment';
    }
  };

  const renderOrderSummary = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Order Summary</Text>
      
      {items.map((item) => (
        <View key={item.id} style={styles.orderItem}>
          <FastImage
            source={{ uri: item.image }}
            style={styles.itemImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <View style={styles.itemDetails}>
            <Text style={styles.itemTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <Text style={styles.itemPrice}>
              ${item.price.toFixed(2)} × {item.quantity}
            </Text>
          </View>
          <Text style={styles.itemTotal}>${item.total.toFixed(2)}</Text>
        </View>
      ))}
    </View>
  );

  const renderShippingAddress = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Shipping Address</Text>
        <TouchableOpacity onPress={() => setShowAddressModal(true)}>
          <Text style={styles.changeText}>Change</Text>
        </TouchableOpacity>
      </View>
      
      {selectedAddress ? (
        <View style={styles.addressCard}>
          <Text style={styles.addressName}>{selectedAddress.name}</Text>
          <Text style={styles.addressPhone}>{selectedAddress.phone}</Text>
          <Text style={styles.addressText}>
            {selectedAddress.address}
          </Text>
          <Text style={styles.addressText}>
            {selectedAddress.city}, {selectedAddress.state} {selectedAddress.zipCode}
          </Text>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.selectButton}
          onPress={() => setShowAddressModal(true)}
        >
          <Text style={styles.selectButtonText}>Select Shipping Address</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderPaymentMethod = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Payment Method</Text>
        <TouchableOpacity onPress={() => setShowPaymentModal(true)}>
          <Text style={styles.changeText}>Change</Text>
        </TouchableOpacity>
      </View>
      
      {selectedPayment ? (
        <View style={styles.paymentCard}>
          <Icon
            name={getPaymentIcon(selectedPayment.type)}
            size={24}
            color="#666666"
          />
          <View style={styles.paymentDetails}>
            <Text style={styles.paymentName}>{selectedPayment.name}</Text>
            <Text style={styles.paymentInfo}>{selectedPayment.details}</Text>
          </View>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.selectButton}
          onPress={() => setShowPaymentModal(true)}
        >
          <Text style={styles.selectButtonText}>Select Payment Method</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderPriceSummary = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Price Details</Text>
      
      <View style={styles.priceRow}>
        <Text style={styles.priceLabel}>Subtotal</Text>
        <Text style={styles.priceValue}>${subtotal.toFixed(2)}</Text>
      </View>
      
      <View style={styles.priceRow}>
        <Text style={styles.priceLabel}>Shipping</Text>
        <Text style={styles.priceValue}>
          {shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}
        </Text>
      </View>
      
      <View style={styles.priceRow}>
        <Text style={styles.priceLabel}>Tax</Text>
        <Text style={styles.priceValue}>${tax.toFixed(2)}</Text>
      </View>
      
      {discount > 0 && (
        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, styles.discountLabel]}>Discount</Text>
          <Text style={[styles.priceValue, styles.discountValue]}>
            -${discount.toFixed(2)}
          </Text>
        </View>
      )}
      
      <View style={styles.separator} />
      
      <View style={styles.priceRow}>
        <Text style={styles.totalLabel}>Total</Text>
        <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
      </View>
    </View>
  );

  const renderAddressModal = () => (
    <Modal
      visible={showAddressModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowAddressModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Address</Text>
            <TouchableOpacity onPress={() => setShowAddressModal(false)}>
              <Icon name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalItem}>
            <Text style={styles.modalItemTitle}>No addresses available</Text>
            <Text style={styles.modalItemSubtitle}>Please add an address first</Text>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderPaymentModal = () => (
    <Modal
      visible={showPaymentModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowPaymentModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Payment</Text>
            <TouchableOpacity onPress={() => setShowPaymentModal(false)}>
              <Icon name="close" size={24} color="#666666" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.modalItem}>
            <Text style={styles.modalItemTitle}>No payment methods available</Text>
            <Text style={styles.modalItemSubtitle}>Please add a payment method first</Text>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderOrderSummary()}
        {renderShippingAddress()}
        {renderPaymentMethod()}
        {renderPriceSummary()}
      </ScrollView>
      
      {/* Place Order Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[styles.placeOrderButton, isProcessing && styles.disabledButton]}
          onPress={handlePlaceOrder}
          disabled={isProcessing}
        >
          <Text style={styles.placeOrderText}>
            {isProcessing ? 'Processing...' : `Place Order - $${total.toFixed(2)}`}
          </Text>
        </TouchableOpacity>
      </View>
      
      {renderAddressModal()}
      {renderPaymentModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  changeText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  itemImage: {
    width: 50,
    height: 50,
    borderRadius: 6,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 14,
    color: '#000000',
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: 12,
    color: '#666666',
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
  },
  addressCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
  },
  addressName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  addressPhone: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 18,
  },
  paymentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
  },
  paymentDetails: {
    marginLeft: 12,
  },
  paymentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  paymentInfo: {
    fontSize: 14,
    color: '#666666',
  },
  selectButton: {
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  selectButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
    color: '#666666',
  },
  priceValue: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '500',
  },
  discountLabel: {
    color: '#4CAF50',
  },
  discountValue: {
    color: '#4CAF50',
  },
  separator: {
    height: 1,
    backgroundColor: '#E5E5EA',
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  bottomContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  placeOrderButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  placeOrderText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  selectedModalItem: {
    backgroundColor: '#F0F8FF',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemContent: {
    flex: 1,
  },
  modalItemTitle: {
    fontSize: 16,
    color: '#000000',
    marginBottom: 2,
  },
  modalItemSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
});

export default CheckoutScreen;
