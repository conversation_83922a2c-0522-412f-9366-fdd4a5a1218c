import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { logoutAsync } from '../../store/slices/authSlice';
import { updateNotificationSettings, setTheme } from '../../store/slices/appSlice';
import { RootStackParamList } from '../../navigation/AppNavigator';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface MenuSection {
  title: string;
  items: MenuItem[];
}

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
  showArrow?: boolean;
}

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const dispatch = useAppDispatch();
  
  const { user } = useAppSelector((state) => state.auth);
  const { notifications, theme } = useAppSelector((state) => state.app);
  
  const [isDarkMode, setIsDarkMode] = useState(theme === 'dark');
  const [pushNotifications, setPushNotifications] = useState(notifications.push);

  const handleEditProfile = () => {
    // TODO: Navigate to edit profile screen
    console.log('Edit profile');
  };

  const handleOrderHistory = () => {
    navigation.navigate('Orders' as any);
  };

  const handleWishlist = () => {
    navigation.navigate('Wishlist' as any);
  };

  const handleAddresses = () => {
    // TODO: Navigate to addresses screen
    console.log('Manage addresses');
  };

  const handlePaymentMethods = () => {
    // TODO: Navigate to payment methods screen
    console.log('Payment methods');
  };

  const handleSettings = () => {
    // TODO: Navigate to settings screen
    console.log('Settings');
  };

  const handleSupport = () => {
    navigation.navigate('Contact');
  };

  const handleFAQ = () => {
    navigation.navigate('FAQ');
  };

  const handleBlog = () => {
    navigation.navigate('Blog');
  };

  const handleServices = () => {
    navigation.navigate('Services');
  };

  const handleTrackOrder = () => {
    navigation.navigate('TrackOrder');
  };

  const handleAbout = () => {
    // TODO: Navigate to about screen
    console.log('About');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(logoutAsync()).unwrap();
            } catch (error) {
              console.error('Logout failed:', error);
            }
          },
        },
      ]
    );
  };

  const handleDarkModeToggle = (value: boolean) => {
    setIsDarkMode(value);
    dispatch(setTheme(value ? 'dark' : 'light'));
  };

  const handleNotificationToggle = (value: boolean) => {
    setPushNotifications(value);
    dispatch(updateNotificationSettings({ push: value }));
  };

  const menuSections: MenuSection[] = [
    {
      title: 'Account',
      items: [
        {
          id: 'edit-profile',
          title: 'Edit Profile',
          icon: 'person',
          onPress: handleEditProfile,
          showArrow: true,
        },
        {
          id: 'orders',
          title: 'Order History',
          icon: 'shopping-bag',
          onPress: handleOrderHistory,
          showArrow: true,
        },
        {
          id: 'wishlist',
          title: 'Wishlist',
          icon: 'favorite',
          onPress: handleWishlist,
          showArrow: true,
        },
        {
          id: 'addresses',
          title: 'Shipping Addresses',
          icon: 'location-on',
          onPress: handleAddresses,
          showArrow: true,
        },
        {
          id: 'payment',
          title: 'Payment Methods',
          icon: 'payment',
          onPress: handlePaymentMethods,
          showArrow: true,
        },
      ],
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'dark-mode',
          title: 'Dark Mode',
          icon: 'dark-mode',
          rightComponent: (
            <Switch
              value={isDarkMode}
              onValueChange={handleDarkModeToggle}
              trackColor={{ false: '#E0E0E0', true: '#007AFF' }}
              thumbColor={isDarkMode ? '#FFFFFF' : '#FFFFFF'}
            />
          ),
        },
        {
          id: 'notifications',
          title: 'Push Notifications',
          icon: 'notifications',
          rightComponent: (
            <Switch
              value={pushNotifications}
              onValueChange={handleNotificationToggle}
              trackColor={{ false: '#E0E0E0', true: '#007AFF' }}
              thumbColor={pushNotifications ? '#FFFFFF' : '#FFFFFF'}
            />
          ),
        },
        {
          id: 'settings',
          title: 'Settings',
          icon: 'settings',
          onPress: handleSettings,
          showArrow: true,
        },
      ],
    },
    {
      title: 'Services',
      items: [
        {
          id: 'track-order',
          title: 'Track Order',
          icon: 'local-shipping',
          onPress: handleTrackOrder,
          showArrow: true,
        },
        {
          id: 'services',
          title: 'Our Services',
          icon: 'star',
          onPress: handleServices,
          showArrow: true,
        },
        {
          id: 'blog',
          title: 'Blog & News',
          icon: 'article',
          onPress: handleBlog,
          showArrow: true,
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'faq',
          title: 'FAQ',
          icon: 'help-outline',
          onPress: handleFAQ,
          showArrow: true,
        },
        {
          id: 'contact',
          title: 'Contact Us',
          icon: 'chat',
          onPress: handleSupport,
          showArrow: true,
        },
        {
          id: 'about',
          title: 'About E-Luxe',
          icon: 'info',
          onPress: handleAbout,
          showArrow: true,
        },
      ],
    },
  ];

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
      disabled={!item.onPress}
    >
      <View style={styles.menuItemLeft}>
        <View style={styles.menuItemIcon}>
          <Icon name={item.icon} size={20} color="#666666" />
        </View>
        <Text style={styles.menuItemTitle}>{item.title}</Text>
      </View>
      
      <View style={styles.menuItemRight}>
        {item.rightComponent}
        {item.showArrow && (
          <Icon name="chevron-right" size={20} color="#C7C7CC" />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderMenuSection = (section: MenuSection) => (
    <View key={section.title} style={styles.menuSection}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.sectionContent}>
        {section.items.map(renderMenuItem)}
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <FastImage
            source={{
              uri: user?.avatar || 'https://via.placeholder.com/100x100/007AFF/FFFFFF?text=User',
            }}
            style={styles.avatar}
            resizeMode={FastImage.resizeMode.cover}
          />
          <TouchableOpacity style={styles.editAvatarButton} onPress={handleEditProfile}>
            <Icon name="camera-alt" size={16} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.profileInfo}>
          <Text style={styles.userName}>
            {user?.username || 'Guest User'}
          </Text>
          <Text style={styles.userEmail}>
            {user?.email || '<EMAIL>'}
          </Text>
          
          <TouchableOpacity style={styles.editProfileButton} onPress={handleEditProfile}>
            <Icon name="edit" size={16} color="#007AFF" />
            <Text style={styles.editProfileText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>12</Text>
          <Text style={styles.statLabel}>Orders</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>5</Text>
          <Text style={styles.statLabel}>Wishlist</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>3</Text>
          <Text style={styles.statLabel}>Reviews</Text>
        </View>
      </View>

      {/* Menu Sections */}
      {menuSections.map(renderMenuSection)}

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Icon name="logout" size={20} color="#FF3B30" />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      {/* App Version */}
      <View style={styles.versionContainer}>
        <Text style={styles.versionText}>E-Luxe v1.0.0</Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  profileHeader: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#007AFF',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 20,
  },
  editProfileText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: 6,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
    marginTop: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#E5E5EA',
  },
  menuSection: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
    paddingHorizontal: 20,
    paddingVertical: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E5EA',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuItemTitle: {
    fontSize: 16,
    color: '#000000',
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoutContainer: {
    marginTop: 24,
    paddingHorizontal: 20,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  logoutText: {
    fontSize: 16,
    color: '#FF3B30',
    fontWeight: '500',
    marginLeft: 8,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  versionText: {
    fontSize: 12,
    color: '#999999',
  },
});

export default ProfileScreen;
