import { ProductRepository } from '../../repositories/ProductRepository';
import { Product, ProductFilter } from '../../entities/Product';

export class GetProductsUseCase {
  constructor(private productRepository: ProductRepository) {}

  async execute(filter?: ProductFilter): Promise<{
    products: Product[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      // Set default filter values
      const defaultFilter: ProductFilter = {
        page: 1,
        limit: 20,
        sortBy: 'newest',
        ...filter,
      };

      // Validate filter parameters
      this.validateFilter(defaultFilter);

      const result = await this.productRepository.getProducts(defaultFilter);

      // Filter out hidden products for non-admin users
      const visibleProducts = result.products.filter(product => product.status === 'show');

      return {
        ...result,
        products: visibleProducts,
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to fetch products: ${error.message}`);
      }
      throw new Error('Failed to fetch products: Unknown error');
    }
  }

  private validateFilter(filter: ProductFilter): void {
    if (filter.page && filter.page < 1) {
      throw new Error('Page number must be greater than 0');
    }

    if (filter.limit && (filter.limit < 1 || filter.limit > 100)) {
      throw new Error('Limit must be between 1 and 100');
    }

    if (filter.minPrice && filter.minPrice < 0) {
      throw new Error('Minimum price cannot be negative');
    }

    if (filter.maxPrice && filter.maxPrice < 0) {
      throw new Error('Maximum price cannot be negative');
    }

    if (filter.minPrice && filter.maxPrice && filter.minPrice > filter.maxPrice) {
      throw new Error('Minimum price cannot be greater than maximum price');
    }

    const validSortOptions = ['price_asc', 'price_desc', 'name_asc', 'name_desc', 'newest', 'rating'];
    if (filter.sortBy && !validSortOptions.includes(filter.sortBy)) {
      throw new Error('Invalid sort option');
    }
  }
}
