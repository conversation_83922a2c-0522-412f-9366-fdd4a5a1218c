import { Order, Cart, CartItem, ShippingAddress, Coupon, OrderTracking } from '../entities/Order';

export interface OrderRepository {
  // Order Management
  getOrders(userId: string, page?: number): Promise<{
    orders: Order[];
    total: number;
    page: number;
    totalPages: number;
  }>;
  getOrderById(orderId: string): Promise<Order>;
  createOrder(orderData: Omit<Order, 'id' | 'order_number' | 'created_at' | 'updated_at'>): Promise<Order>;
  updateOrderStatus(orderId: string, status: Order['status']): Promise<Order>;
  cancelOrder(orderId: string): Promise<Order>;
  
  // Cart Management
  getCart(userId: string): Promise<Cart>;
  addToCart(userId: string, item: Omit<CartItem, 'id' | 'total'>): Promise<Cart>;
  updateCartItem(userId: string, itemId: string, quantity: number): Promise<Cart>;
  removeFromCart(userId: string, itemId: string): Promise<Cart>;
  clearCart(userId: string): Promise<void>;
  
  // Shipping Addresses
  getShippingAddresses(userId: string): Promise<ShippingAddress[]>;
  addShippingAddress(userId: string, address: Omit<ShippingAddress, 'id' | 'userId' | 'created_at' | 'updated_at'>): Promise<ShippingAddress>;
  updateShippingAddress(addressId: string, address: Partial<ShippingAddress>): Promise<ShippingAddress>;
  deleteShippingAddress(addressId: string): Promise<void>;
  setDefaultShippingAddress(userId: string, addressId: string): Promise<void>;
  
  // Coupons
  validateCoupon(code: string, cartTotal: number): Promise<Coupon>;
  applyCoupon(userId: string, code: string): Promise<Cart>;
  removeCoupon(userId: string): Promise<Cart>;
  getAvailableCoupons(userId: string): Promise<Coupon[]>;
  
  // Order Tracking
  getOrderTracking(orderId: string): Promise<OrderTracking>;
  
  // Shipping Options
  getShippingOptions(address: ShippingAddress, cartItems: CartItem[]): Promise<{
    id: string;
    name: string;
    description: string;
    price: number;
    estimatedDays: number;
  }[]>;
  
  // Payment
  processPayment(orderId: string, paymentData: {
    method: 'stripe' | 'razorpay' | 'paypal';
    token: string;
    amount: number;
  }): Promise<{
    success: boolean;
    transactionId: string;
    message: string;
  }>;
}

export interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  cart: Cart;
  shippingAddresses: ShippingAddress[];
  orderTracking: OrderTracking | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    totalPages: number;
    total: number;
  };
}
