import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// State interface
interface AppState {
  language: string;
  currency: string;
  theme: 'light' | 'dark' | 'auto';
  isOnline: boolean;
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
  };
  onboarding: {
    completed: boolean;
    currentStep: number;
  };
  settings: {
    biometricAuth: boolean;
    autoSync: boolean;
    dataUsage: 'wifi' | 'cellular' | 'both';
  };
}

// Initial state
const initialState: AppState = {
  language: 'en',
  currency: 'USD',
  theme: 'light',
  isOnline: true,
  notifications: {
    push: true,
    email: true,
    sms: false,
    orderUpdates: true,
    promotions: true,
    newsletter: false,
  },
  onboarding: {
    completed: false,
    currentStep: 0,
  },
  settings: {
    biometricAuth: false,
    autoSync: true,
    dataUsage: 'wifi',
  },
};

// Slice
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
    },
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    updateNotificationSettings: (state, action: PayloadAction<Partial<AppState['notifications']>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    setOnboardingCompleted: (state) => {
      state.onboarding.completed = true;
    },
    setOnboardingStep: (state, action: PayloadAction<number>) => {
      state.onboarding.currentStep = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<AppState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    resetApp: (state) => {
      return {
        ...initialState,
        language: state.language,
        currency: state.currency,
        theme: state.theme,
      };
    },
  },
});

export const {
  setLanguage,
  setCurrency,
  setTheme,
  setOnlineStatus,
  updateNotificationSettings,
  setOnboardingCompleted,
  setOnboardingStep,
  updateSettings,
  resetApp,
} = appSlice.actions;

export default appSlice.reducer;
