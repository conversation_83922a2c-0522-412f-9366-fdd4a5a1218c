module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
        alias: {
          '@': './src',
          '@/components': './src/presentation/components',
          '@/screens': './src/presentation/screens',
          '@/navigation': './src/presentation/navigation',
          '@/hooks': './src/presentation/hooks',
          '@/store': './src/presentation/store',
          '@/domain': './src/domain',
          '@/data': './src/data',
          '@/utils': './src/utils',
          '@/types': './src/types',
          '@/constants': './src/constants',
        },
      },
    ],
    // 'react-native-reanimated/plugin', // Temporairement désactivé
  ],
};
