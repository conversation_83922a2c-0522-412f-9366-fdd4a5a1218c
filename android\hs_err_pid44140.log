#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 215056 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=44140, tid=29784
#
# JRE version: Java(TM) SE Runtime Environment (17.0.11+7) (build 17.0.11+7-LTS-207)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1

Host: Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sat Jun 21 04:52:24 2025 Mauritius Standard Time elapsed time: 65.957310 seconds (0d 0h 1m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000002c17f91b960):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=29784, stack(0x000000cc64500000,0x000000cc64600000)]


Current CompileTask:
C2:  65957 3837       4       java.util.AbstractCollection::addAll (42 bytes)

Stack: [0x000000cc64500000,0x000000cc64600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x679cca]
V  [jvm.dll+0x7da13d]
V  [jvm.dll+0x7dba83]
V  [jvm.dll+0x7dc0f3]
V  [jvm.dll+0x2449af]
V  [jvm.dll+0xab83b]
V  [jvm.dll+0xabe1c]
V  [jvm.dll+0x562ef1]
V  [jvm.dll+0x32bd90]
V  [jvm.dll+0x32b1fa]
V  [jvm.dll+0x2161df]
V  [jvm.dll+0x21560f]
V  [jvm.dll+0x1a2040]
V  [jvm.dll+0x225a2b]
V  [jvm.dll+0x223bcb]
V  [jvm.dll+0x7903ec]
V  [jvm.dll+0x78a85a]
V  [jvm.dll+0x678bb5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002c1319298c0, length=36, elements={
0x000002c172267600, 0x000002c17f8ffa30, 0x000002c17f902080, 0x000002c17f9172e0,
0x000002c17f917bb0, 0x000002c17f91a490, 0x000002c17f91ad60, 0x000002c17f91b960,
0x000002c17fa30080, 0x000002c17fa96bf0, 0x000002c17fb75c90, 0x000002c17fb7bac0,
0x000002c13084f840, 0x000002c1316454d0, 0x000002c17fbe86b0, 0x000002c13139fc20,
0x000002c1310ee460, 0x000002c130e19350, 0x000002c130e18420, 0x000002c130e1aca0,
0x000002c130e174f0, 0x000002c130e1a790, 0x000002c130e17f10, 0x000002c130e18930,
0x000002c130e18e40, 0x000002c130e1a280, 0x000002c130e19860, 0x000002c130e19d70,
0x000002c1319398d0, 0x000002c13193ad10, 0x000002c131939de0, 0x000002c13193c150,
0x000002c1319393c0, 0x000002c13193b730, 0x000002c131938eb0, 0x000002c13193bc40
}

Java Threads: ( => current thread )
  0x000002c172267600 JavaThread "main" [_thread_blocked, id=17132, stack(0x000000cc63800000,0x000000cc63900000)]
  0x000002c17f8ffa30 JavaThread "Reference Handler" daemon [_thread_blocked, id=452, stack(0x000000cc63f00000,0x000000cc64000000)]
  0x000002c17f902080 JavaThread "Finalizer" daemon [_thread_blocked, id=38892, stack(0x000000cc64000000,0x000000cc64100000)]
  0x000002c17f9172e0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=39948, stack(0x000000cc64100000,0x000000cc64200000)]
  0x000002c17f917bb0 JavaThread "Attach Listener" daemon [_thread_blocked, id=44056, stack(0x000000cc64200000,0x000000cc64300000)]
  0x000002c17f91a490 JavaThread "Service Thread" daemon [_thread_blocked, id=44568, stack(0x000000cc64300000,0x000000cc64400000)]
  0x000002c17f91ad60 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=44764, stack(0x000000cc64400000,0x000000cc64500000)]
=>0x000002c17f91b960 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=29784, stack(0x000000cc64500000,0x000000cc64600000)]
  0x000002c17fa30080 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=44756, stack(0x000000cc64600000,0x000000cc64700000)]
  0x000002c17fa96bf0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=1032, stack(0x000000cc64700000,0x000000cc64800000)]
  0x000002c17fb75c90 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=19320, stack(0x000000cc64800000,0x000000cc64900000)]
  0x000002c17fb7bac0 JavaThread "Notification Thread" daemon [_thread_blocked, id=44268, stack(0x000000cc64900000,0x000000cc64a00000)]
  0x000002c13084f840 JavaThread "Daemon health stats" [_thread_blocked, id=46756, stack(0x000000cc65000000,0x000000cc65100000)]
  0x000002c1316454d0 JavaThread "Incoming local TCP Connector on port 51119" [_thread_in_native, id=47568, stack(0x000000cc65100000,0x000000cc65200000)]
  0x000002c17fbe86b0 JavaThread "Daemon periodic checks" [_thread_blocked, id=47640, stack(0x000000cc65200000,0x000000cc65300000)]
  0x000002c13139fc20 JavaThread "Daemon" [_thread_blocked, id=47900, stack(0x000000cc65300000,0x000000cc65400000)]
  0x000002c1310ee460 JavaThread "Handler for socket connection from /127.0.0.1:51119 to /127.0.0.1:51120" [_thread_in_native, id=47904, stack(0x000000cc65400000,0x000000cc65500000)]
  0x000002c130e19350 JavaThread "Cancel handler" [_thread_blocked, id=48000, stack(0x000000cc65500000,0x000000cc65600000)]
  0x000002c130e18420 JavaThread "Daemon worker" [_thread_in_native, id=48004, stack(0x000000cc65600000,0x000000cc65700000)]
  0x000002c130e1aca0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51119 to /127.0.0.1:51120" [_thread_blocked, id=48064, stack(0x000000cc65700000,0x000000cc65800000)]
  0x000002c130e174f0 JavaThread "Stdin handler" [_thread_blocked, id=48068, stack(0x000000cc65800000,0x000000cc65900000)]
  0x000002c130e1a790 JavaThread "Daemon client event forwarder" [_thread_blocked, id=48120, stack(0x000000cc65900000,0x000000cc65a00000)]
  0x000002c130e17f10 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=47776, stack(0x000000cc65a00000,0x000000cc65b00000)]
  0x000002c130e18930 JavaThread "File lock request listener" [_thread_in_native, id=47488, stack(0x000000cc65b00000,0x000000cc65c00000)]
  0x000002c130e18e40 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.0.1\fileHashes)" [_thread_blocked, id=43028, stack(0x000000cc65c00000,0x000000cc65d00000)]
  0x000002c130e1a280 JavaThread "File lock release action executor" [_thread_blocked, id=38976, stack(0x000000cc65e00000,0x000000cc65f00000)]
  0x000002c130e19860 JavaThread "File watcher server" daemon [_thread_in_native, id=41048, stack(0x000000cc65f00000,0x000000cc66000000)]
  0x000002c130e19d70 JavaThread "File watcher consumer" daemon [_thread_blocked, id=20464, stack(0x000000cc66000000,0x000000cc66100000)]
  0x000002c1319398d0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\.gradle\8.0.1\checksums)" [_thread_blocked, id=48476, stack(0x000000cc65d00000,0x000000cc65e00000)]
  0x000002c13193ad10 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.0.1\md-rule)" [_thread_blocked, id=48640, stack(0x000000cc66100000,0x000000cc66200000)]
  0x000002c131939de0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.0.1\fileContent)" [_thread_blocked, id=48696, stack(0x000000cc66200000,0x000000cc66300000)]
  0x000002c13193c150 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\.gradle\8.0.1\fileHashes)" [_thread_blocked, id=49848, stack(0x000000cc66300000,0x000000cc66400000)]
  0x000002c1319393c0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.0.1\md-supplier)" [_thread_blocked, id=20096, stack(0x000000cc66a00000,0x000000cc66b00000)]
  0x000002c13193b730 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.0.1\executionHistory)" [_thread_blocked, id=50256, stack(0x000000cc63500000,0x000000cc63600000)]
  0x000002c131938eb0 JavaThread "jar transforms" [_thread_blocked, id=33040, stack(0x000000cc66d00000,0x000000cc66e00000)]
  0x000002c13193bc40 JavaThread "jar transforms Thread 2" [_thread_blocked, id=51744, stack(0x000000cc63600000,0x000000cc63700000)]

Other Threads:
  0x000002c17f8f91c0 VMThread "VM Thread" [stack: 0x000000cc63e00000,0x000000cc63f00000] [id=35444]
  0x000002c17fb7bfa0 WatcherThread [stack: 0x000000cc64a00000,0x000000cc64b00000] [id=44284]
  0x000002c1722c1d30 GCTaskThread "GC Thread#0" [stack: 0x000000cc63900000,0x000000cc63a00000] [id=44468]
  0x000002c131108c60 GCTaskThread "GC Thread#1" [stack: 0x000000cc64b00000,0x000000cc64c00000] [id=45848]
  0x000002c130d63160 GCTaskThread "GC Thread#2" [stack: 0x000000cc64c00000,0x000000cc64d00000] [id=45852]
  0x000002c130d63420 GCTaskThread "GC Thread#3" [stack: 0x000000cc64d00000,0x000000cc64e00000] [id=45856]
  0x000002c130d636e0 GCTaskThread "GC Thread#4" [stack: 0x000000cc64e00000,0x000000cc64f00000] [id=45860]
  0x000002c1301e8020 GCTaskThread "GC Thread#5" [stack: 0x000000cc64f00000,0x000000cc65000000] [id=45864]
  0x000002c131f88cd0 GCTaskThread "GC Thread#6" [stack: 0x000000cc66400000,0x000000cc66500000] [id=50128]
  0x000002c131f58100 GCTaskThread "GC Thread#7" [stack: 0x000000cc66500000,0x000000cc66600000] [id=50132]
  0x000002c131ff6840 GCTaskThread "GC Thread#8" [stack: 0x000000cc66600000,0x000000cc66700000] [id=50136]
  0x000002c130fb4b00 GCTaskThread "GC Thread#9" [stack: 0x000000cc66700000,0x000000cc66800000] [id=50140]
  0x000002c1722d4bb0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000cc63a00000,0x000000cc63b00000] [id=44964]
  0x000002c1722d54d0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000cc63b00000,0x000000cc63c00000] [id=44516]
  0x000002c130e14820 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000cc66800000,0x000000cc66900000] [id=37580]
  0x000002c130e14ae0 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000cc66900000,0x000000cc66a00000] [id=37416]
  0x000002c17232dc30 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000cc63c00000,0x000000cc63d00000] [id=27772]
  0x000002c17232e6d0 ConcurrentGCThread "G1 Service" [stack: 0x000000cc63d00000,0x000000cc63e00000] [id=44508]

Threads with active compile tasks:
C2 CompilerThread0    66147 3837       4       java.util.AbstractCollection::addAll (42 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002c111000000-0x000002c111bd0000-0x000002c111bd0000), size 12386304, SharedBaseAddress: 0x000002c111000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002c112000000-0x000002c12c000000, reserved size: 436207616
Narrow klass base: 0x000002c111000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 102400K, used 40976K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 6 survivors (6144K)
 Metaspace       used 30431K, committed 30848K, reserved 491520K
  class space    used 4266K, committed 4480K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000, 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000, 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080b4b400, 0x0000000080c00000| 29%| O|  |TAMS 0x0000000080b4b400, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081643000, 0x0000000081700000| 26%| O|  |TAMS 0x0000000081600000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081700000, 0x0000000081800000|  0%| F|  |TAMS 0x0000000081700000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082475f80, 0x0000000082500000| 46%| S|CS|TAMS 0x0000000082400000, 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| S|CS|TAMS 0x0000000082500000, 0x0000000082500000| Complete 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| S|CS|TAMS 0x0000000082600000, 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| S|CS|TAMS 0x0000000082700000, 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| S|CS|TAMS 0x0000000082800000, 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| S|CS|TAMS 0x0000000082900000, 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a85050, 0x0000000088b00000| 51%| E|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Complete 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| E|CS|TAMS 0x0000000088b00000, 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| E|CS|TAMS 0x0000000088c00000, 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| E|CS|TAMS 0x0000000088d00000, 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| E|CS|TAMS 0x0000000088e00000, 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| E|CS|TAMS 0x0000000088f00000, 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| E|CS|TAMS 0x0000000089000000, 0x0000000089000000| Complete 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| E|CS|TAMS 0x0000000089100000, 0x0000000089100000| Complete 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| E|CS|TAMS 0x0000000089200000, 0x0000000089200000| Complete 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| E|  |TAMS 0x0000000089300000, 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| E|CS|TAMS 0x0000000089400000, 0x0000000089400000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000, 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000, 0x000000008f800000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x000002c17a470000,0x000002c17a870000] _byte_map_base: 0x000002c17a070000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002c1722c3270, (CMBitMap*) 0x000002c1722c32b0
 Prev Bits: [0x000002c17ac70000, 0x000002c17cc70000)
 Next Bits: [0x000002c17cc70000, 0x000002c17ec70000)

Polling page: 0x000002c1719a0000

Metaspace:

Usage:
  Non-class:     25.55 MB used.
      Class:      4.17 MB used.
       Both:     29.72 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      25.75 MB ( 40%) committed,  1 nodes.
      Class space:      416.00 MB reserved,       4.38 MB (  1%) committed,  1 nodes.
             Both:      480.00 MB reserved,      30.12 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  5.69 MB
       Class:  11.66 MB
        Both:  17.35 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 47.62 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 318.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 482.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 1360.
num_chunk_merges: 9.
num_chunk_splits: 929.
num_chunks_enlarged: 672.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1978Kb max_used=1978Kb free=118021Kb
 bounds [0x000002c107ad0000, 0x000002c107d40000, 0x000002c10f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=8099Kb max_used=8099Kb free=111900Kb
 bounds [0x000002c100000000, 0x000002c1007f0000, 0x000002c107530000]
CodeHeap 'non-nmethods': size=5760Kb used=1586Kb max_used=1638Kb free=4173Kb
 bounds [0x000002c107530000, 0x000002c1077a0000, 0x000002c107ad0000]
 total_blobs=4512 nmethods=3865 adapters=559
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 63.889 Thread 0x000002c17fa30080 nmethod 3859 0x000002c1007da990 code [0x000002c1007dad20, 0x000002c1007dcb08]
Event: 63.889 Thread 0x000002c17fa30080 3872       3       java.lang.Class::getPackageName (81 bytes)
Event: 63.890 Thread 0x000002c17fa30080 nmethod 3872 0x000002c1007dd390 code [0x000002c1007dd620, 0x000002c1007de0c8]
Event: 63.890 Thread 0x000002c17fa30080 3853       3       java.lang.reflect.Executable::printModifiersIfNonzero (101 bytes)
Event: 63.891 Thread 0x000002c17fa30080 nmethod 3853 0x000002c1007de490 code [0x000002c1007de720, 0x000002c1007def48]
Event: 63.891 Thread 0x000002c17fa30080 3867       1       groovy.lang.MetaBeanProperty::getGetter (5 bytes)
Event: 63.891 Thread 0x000002c17fa30080 nmethod 3867 0x000002c107cbdd90 code [0x000002c107cbdf20, 0x000002c107cbdff8]
Event: 63.891 Thread 0x000002c17fa30080 3868       1       groovy.lang.MetaBeanProperty::getField (5 bytes)
Event: 63.891 Thread 0x000002c17fa30080 nmethod 3868 0x000002c107cbe090 code [0x000002c107cbe220, 0x000002c107cbe2f8]
Event: 63.891 Thread 0x000002c17fa30080 3854       3       java.util.Collections$UnmodifiableMap$UnmodifiableEntrySet$1::next (5 bytes)
Event: 63.891 Thread 0x000002c17fa30080 nmethod 3854 0x000002c1007df290 code [0x000002c1007df480, 0x000002c1007dfbd8]
Event: 63.891 Thread 0x000002c17fa30080 3863       1       java.lang.reflect.Field::getType (5 bytes)
Event: 63.891 Thread 0x000002c17fa30080 nmethod 3863 0x000002c107cbe390 code [0x000002c107cbe520, 0x000002c107cbe5f8]
Event: 63.891 Thread 0x000002c17fa30080 3869       3       org.codehaus.groovy.runtime.metaclass.MetaMethodIndex::hash (31 bytes)
Event: 63.892 Thread 0x000002c17fa30080 nmethod 3869 0x000002c1007dfe10 code [0x000002c1007dffa0, 0x000002c1007e00b8]
Event: 63.892 Thread 0x000002c17fa30080 3871       1       groovy.lang.MetaBeanProperty::setGetter (6 bytes)
Event: 63.892 Thread 0x000002c17fa30080 nmethod 3871 0x000002c107cbe690 code [0x000002c107cbe820, 0x000002c107cbe938]
Event: 63.892 Thread 0x000002c17fa30080 3870       3       org.codehaus.groovy.reflection.CachedClass::getName (8 bytes)
Event: 63.892 Thread 0x000002c17fa30080 nmethod 3870 0x000002c1007e0190 code [0x000002c1007e0340, 0x000002c1007e0548]
Event: 64.500 Thread 0x000002c17fa30080 3873       3       java.util.HashMap$ValueSpliterator::forEachRemaining (186 bytes)

GC Heap History (8 events):
Event: 2.374 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 26624K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 3025K, committed 3200K, reserved 491520K
  class space    used 380K, committed 448K, reserved 425984K
}
Event: 2.580 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 10068K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 3025K, committed 3200K, reserved 491520K
  class space    used 380K, committed 448K, reserved 425984K
}
Event: 5.320 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 40788K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 32 young (32768K), 3 survivors (3072K)
 Metaspace       used 3947K, committed 4096K, reserved 491520K
  class space    used 476K, committed 576K, reserved 425984K
}
Event: 5.324 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 14636K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 3947K, committed 4096K, reserved 491520K
  class space    used 476K, committed 576K, reserved 425984K
}
Event: 27.910 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 117036K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 105 young (107520K), 4 survivors (4096K)
 Metaspace       used 21250K, committed 21504K, reserved 491520K
  class space    used 3143K, committed 3264K, reserved 425984K
}
Event: 29.289 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 22829K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 21250K, committed 21504K, reserved 491520K
  class space    used 3143K, committed 3264K, reserved 425984K
}
Event: 39.979 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 106496K, used 73005K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 62 young (63488K), 12 survivors (12288K)
 Metaspace       used 28890K, committed 29248K, reserved 491520K
  class space    used 4043K, committed 4160K, reserved 425984K
}
Event: 39.991 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 106496K, used 27664K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 28890K, committed 29248K, reserved 491520K
  class space    used 4043K, committed 4160K, reserved 425984K
}

Deoptimization events (20 events):
Event: 63.454 Thread 0x000002c130e18420 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002c107c91ba4 relative=0x0000000000000444
Event: 63.454 Thread 0x000002c130e18420 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002c107c91ba4 method=java.util.regex.Pattern$Start.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 44 c2
Event: 63.454 Thread 0x000002c130e18420 DEOPT PACKING pc=0x000002c107c91ba4 sp=0x000000cc656f81c0
Event: 63.454 Thread 0x000002c130e18420 DEOPT UNPACKING pc=0x000002c1075823a3 sp=0x000000cc656f8088 mode 2
Event: 63.459 Thread 0x000002c130e18420 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002c107cb8964 relative=0x00000000000006a4
Event: 63.459 Thread 0x000002c130e18420 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002c107cb8964 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 63.459 Thread 0x000002c130e18420 DEOPT PACKING pc=0x000002c107cb8964 sp=0x000000cc656f81e0
Event: 63.459 Thread 0x000002c130e18420 DEOPT UNPACKING pc=0x000002c1075823a3 sp=0x000000cc656f8198 mode 2
Event: 63.459 Thread 0x000002c130e18420 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002c107cb8964 relative=0x00000000000006a4
Event: 63.459 Thread 0x000002c130e18420 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002c107cb8964 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 63.459 Thread 0x000002c130e18420 DEOPT PACKING pc=0x000002c107cb8964 sp=0x000000cc656f81e0
Event: 63.460 Thread 0x000002c130e18420 DEOPT UNPACKING pc=0x000002c1075823a3 sp=0x000000cc656f8198 mode 2
Event: 63.671 Thread 0x000002c130e18420 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002c107cb8964 relative=0x00000000000006a4
Event: 63.671 Thread 0x000002c130e18420 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002c107cb8964 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 63.671 Thread 0x000002c130e18420 DEOPT PACKING pc=0x000002c107cb8964 sp=0x000000cc656f8bb0
Event: 63.671 Thread 0x000002c130e18420 DEOPT UNPACKING pc=0x000002c1075823a3 sp=0x000000cc656f8b68 mode 2
Event: 63.671 Thread 0x000002c130e18420 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002c107cb8964 relative=0x00000000000006a4
Event: 63.671 Thread 0x000002c130e18420 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002c107cb8964 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 63.671 Thread 0x000002c130e18420 DEOPT PACKING pc=0x000002c107cb8964 sp=0x000000cc656f8b90
Event: 63.671 Thread 0x000002c130e18420 DEOPT UNPACKING pc=0x000002c1075823a3 sp=0x000000cc656f8b48 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 63.445 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088e780a8}: settings_ea6p37qstb64plpocmsvy0qw7BeanInfo> (0x0000000088e780a8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.446 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088e82f60}: org/gradle/initialization/SettingsScriptBeanInfo> (0x0000000088e82f60) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.446 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088e8dcd0}: org/gradle/plugin/use/internal/PluginsAwareScriptBeanInfo> (0x0000000088e8dcd0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.447 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088e98908}: org/gradle/groovy/scripts/DefaultScriptBeanInfo> (0x0000000088e98908) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.447 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088ea34b8}: org/gradle/groovy/scripts/BasicScriptBeanInfo> (0x0000000088ea34b8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.448 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088eadff8}: org/gradle/groovy/scripts/ScriptBeanInfo> (0x0000000088eadff8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.449 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088ec6040}: groovy/lang/ScriptBeanInfo> (0x0000000088ec6040) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.449 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088ede078}: groovy/lang/ScriptCustomizer> (0x0000000088ede078) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.451 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088ef08a0}: org/gradle/groovy/scripts/ScriptCustomizer> (0x0000000088ef08a0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.452 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088d02600}: org/gradle/groovy/scripts/BasicScriptCustomizer> (0x0000000088d02600) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.453 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088d19b00}: org/gradle/groovy/scripts/DefaultScriptCustomizer> (0x0000000088d19b00) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.456 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088d424b0}: org/gradle/plugin/use/internal/PluginsAwareScriptCustomizer> (0x0000000088d424b0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.457 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088d551c0}: org/gradle/initialization/SettingsScriptCustomizer> (0x0000000088d551c0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.458 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088d634a0}: settings_ea6p37qstb64plpocmsvy0qw7Customizer> (0x0000000088d634a0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.825 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088c79d50}: org/gradle/initialization/DefaultSettings_DecoratedBeanInfo> (0x0000000088c79d50) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.826 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088c8a950}: org/gradle/initialization/DefaultSettingsBeanInfo> (0x0000000088c8a950) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.827 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088c9bca0}: org/gradle/api/internal/project/AbstractPluginAwareBeanInfo> (0x0000000088c9bca0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.828 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088cacdd0}: org/gradle/api/internal/project/AbstractPluginAwareCustomizer> (0x0000000088cacdd0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.831 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088cc3780}: org/gradle/initialization/DefaultSettingsCustomizer> (0x0000000088cc3780) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 63.881 Thread 0x000002c130e18420 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088b07b20}: org/gradle/initialization/DefaultSettings_DecoratedCustomizer> (0x0000000088b07b20) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 54.842 Executing VM operation: Cleanup done
Event: 55.201 Executing VM operation: HandshakeAllThreads
Event: 55.358 Executing VM operation: HandshakeAllThreads done
Event: 55.360 Executing VM operation: HandshakeAllThreads
Event: 55.360 Executing VM operation: HandshakeAllThreads done
Event: 56.370 Executing VM operation: Cleanup
Event: 56.370 Executing VM operation: Cleanup done
Event: 59.417 Executing VM operation: Cleanup
Event: 59.418 Executing VM operation: Cleanup done
Event: 60.310 Executing VM operation: HandshakeAllThreads
Event: 60.310 Executing VM operation: HandshakeAllThreads done
Event: 61.324 Executing VM operation: Cleanup
Event: 61.324 Executing VM operation: Cleanup done
Event: 62.343 Executing VM operation: Cleanup
Event: 62.386 Executing VM operation: Cleanup done
Event: 63.396 Executing VM operation: Cleanup
Event: 63.428 Executing VM operation: Cleanup done
Event: 64.437 Executing VM operation: Cleanup
Event: 64.437 Executing VM operation: Cleanup done
Event: 65.449 Executing VM operation: Cleanup

Events (20 events):
Event: 40.337 loading class java/lang/invoke/SerializedLambda
Event: 40.337 loading class java/lang/invoke/SerializedLambda done
Event: 40.337 loading class java/lang/ProcessBuilder
Event: 40.338 loading class java/lang/ProcessBuilder done
Event: 52.751 Thread 0x000002c131938eb0 Thread added: 0x000002c131938eb0
Event: 52.967 loading class sun/nio/fs/WindowsPath$1
Event: 52.967 loading class sun/nio/fs/WindowsPath$1 done
Event: 55.498 loading class java/nio/charset/CharacterCodingException
Event: 55.522 loading class java/nio/charset/CharacterCodingException done
Event: 61.566 Thread 0x000002c13193bc40 Thread added: 0x000002c13193bc40
Event: 62.461 loading class java/util/stream/Streams$RangeIntSpliterator
Event: 62.515 loading class java/util/stream/Streams$RangeIntSpliterator done
Event: 62.534 loading class java/util/stream/IntPipeline$1
Event: 62.542 loading class java/util/stream/IntPipeline$1 done
Event: 62.542 loading class java/util/stream/Nodes$FixedNodeBuilder
Event: 62.542 loading class java/util/stream/Nodes$ArrayNode
Event: 62.545 loading class java/util/stream/Nodes$ArrayNode done
Event: 62.545 loading class java/util/stream/Nodes$FixedNodeBuilder done
Event: 62.546 loading class java/util/stream/IntPipeline$1$1
Event: 62.609 loading class java/util/stream/IntPipeline$1$1 done


Dynamic libraries:
0x00007ff6208b0000 - 0x00007ff6208c0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007fffa6c00000 - 0x00007fffa6e65000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffa49a0000 - 0x00007fffa4a69000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffa40e0000 - 0x00007fffa44c8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffa3f90000 - 0x00007fffa40db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff9cb70000 - 0x00007fff9cb89000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007fffa6b00000 - 0x00007fffa6bb3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffa6070000 - 0x00007fffa6119000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff9c920000 - 0x00007fff9c93b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007fffa6a00000 - 0x00007fffa6aa6000 	C:\WINDOWS\System32\sechost.dll
0x00007fffa57f0000 - 0x00007fffa5905000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffa5350000 - 0x00007fffa551a000 	C:\WINDOWS\System32\USER32.dll
0x00007fffa3ed0000 - 0x00007fffa3ef7000 	C:\WINDOWS\System32\win32u.dll
0x00007fffa4d70000 - 0x00007fffa4d9b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffa4580000 - 0x00007fffa46b7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffa44d0000 - 0x00007fffa4573000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff8ec00000 - 0x00007fff8ee9a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff9be00000 - 0x00007fff9be0b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffa4da0000 - 0x00007fffa4dd0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff9e700000 - 0x00007fff9e70c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007fff68070000 - 0x00007fff680fe000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007fff30bd0000 - 0x00007fff317b0000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007fffa4a70000 - 0x00007fffa4a78000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff89d80000 - 0x00007fff89d8a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fffa6120000 - 0x00007fffa6194000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff97df0000 - 0x00007fff97e25000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffa2c10000 - 0x00007fffa2c2b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff9c910000 - 0x00007fff9c91a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007fffa1570000 - 0x00007fffa17b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffa4fc0000 - 0x00007fffa5345000 	C:\WINDOWS\System32\combase.dll
0x00007fffa55c0000 - 0x00007fffa56a1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff8da10000 - 0x00007fff8da49000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffa4780000 - 0x00007fffa4819000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff5db60000 - 0x00007fff5db85000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffebf6a0000 - 0x00007ffebf777000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007fffa61a0000 - 0x00007fffa68e2000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffa4820000 - 0x00007fffa4994000 	C:\WINDOWS\System32\wintypes.dll
0x00007fffa1970000 - 0x00007fffa21c8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffa4a80000 - 0x00007fffa4b71000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffa5da0000 - 0x00007fffa5e0a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffa3c60000 - 0x00007fffa3c8f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff5db40000 - 0x00007fff5db59000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007fff9c0f0000 - 0x00007fff9c20e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffa31a0000 - 0x00007fffa320a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff3d6e0000 - 0x00007fff3d6f6000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007fff30bb0000 - 0x00007fff30bc8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007fff30ba0000 - 0x00007fff30bb0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007fff90860000 - 0x00007fff90887000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007ffeb12c0000 - 0x00007ffeb1404000 	C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
0x00007fff68060000 - 0x00007fff6806a000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007fff30b90000 - 0x00007fff30b9b000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007fffa3450000 - 0x00007fffa346b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffa2b70000 - 0x00007fffa2baa000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffa3240000 - 0x00007fffa326b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffa3c30000 - 0x00007fffa3c56000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fffa3470000 - 0x00007fffa347c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffa25d0000 - 0x00007fffa2603000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffa68f0000 - 0x00007fffa68fa000 	C:\WINDOWS\System32\NSI.dll
0x00007fff9c660000 - 0x00007fff9c67f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff9c410000 - 0x00007fff9c435000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffa2670000 - 0x00007fffa2797000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fff30b80000 - 0x00007fff30b8e000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007fffa3d50000 - 0x00007fffa3ec7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffa3670000 - 0x00007fffa36a0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffa3620000 - 0x00007fffa365f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff40420000 - 0x00007fff40428000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64;C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.0.1-all\aro4hu1c3oeioove7l0i4i14o\gradle-8.0.1\lib\gradle-launcher-8.0.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
CLASSPATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;%JAVA_HOME%\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\wamp64\bin\php\php8.1.13;C:\ProgramData\ComposerSetup\bin;C:\Program Files\RabbitMQ Server\rabbitmq_server-3.12.2\sbin;C:\Program Files\MongoDB\Tools\100\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\cloudflared\;c:\users\<USER>\pear.bat;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Progr
USERNAME=MSI
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 1 days 5:39 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16228M (150M free)
TotalPageFile size 62338M (AvailPageFile size 175M)
current process WorkingSet (physical memory assigned to process): 71M, peak: 210M
current process commit charge ("private bytes"): 263M, peak: 420M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207) for windows-amd64 JRE (17.0.11+7-LTS-207), built on Mar 11 2024 19:01:50 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
