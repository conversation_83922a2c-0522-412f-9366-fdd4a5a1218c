#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1240640 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=48388, tid=35932
#
# JRE version: Java(TM) SE Runtime Environment (17.0.11+7) (build 17.0.11+7-LTS-207)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1

Host: Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sat Jun 21 04:53:28 2025 Mauritius Standard Time elapsed time: 98.084603 seconds (0d 0h 1m 38s)

---------------  T H R E A D  ---------------

Current thread (0x000002aa6599dfd0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=35932, stack(0x000000ccd6700000,0x000000ccd6800000)]


Current CompileTask:
C2:  98085 2206       4       jdk.internal.org.objectweb.asm.Frame::execute (2305 bytes)

Stack: [0x000000ccd6700000,0x000000ccd6800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x679cca]
V  [jvm.dll+0x7da13d]
V  [jvm.dll+0x7dba83]
V  [jvm.dll+0x7dc0f3]
V  [jvm.dll+0x2449af]
V  [jvm.dll+0xab83b]
V  [jvm.dll+0xabe1c]
V  [jvm.dll+0x3615ae]
V  [jvm.dll+0x1b9dda]
V  [jvm.dll+0x216337]
V  [jvm.dll+0x21560f]
V  [jvm.dll+0x1a2040]
V  [jvm.dll+0x225a2b]
V  [jvm.dll+0x223bcb]
V  [jvm.dll+0x7903ec]
V  [jvm.dll+0x78a85a]
V  [jvm.dll+0x678bb5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002aa65d53510, length=22, elements={
0x000002aa472d8ab0, 0x000002aa65981f70, 0x000002aa65983a50, 0x000002aa65999950,
0x000002aa6599c230, 0x000002aa6599cb00, 0x000002aa6599d3d0, 0x000002aa6599dfd0,
0x000002aa6599e8e0, 0x000002aa65ab1b50, 0x000002aa65b1d070, 0x000002aa65b2b7e0,
0x000002aa6accb500, 0x000002aa6b2d0040, 0x000002aa6ab20ea0, 0x000002aa65d525b0,
0x000002aa6b248420, 0x000002aa65d22a70, 0x000002aa65d5df20, 0x000002aa65d5fd80,
0x000002aa65d5e430, 0x000002aa65d60290
}

Java Threads: ( => current thread )
  0x000002aa472d8ab0 JavaThread "main" [_thread_blocked, id=51292, stack(0x000000ccd5a00000,0x000000ccd5b00000)]
  0x000002aa65981f70 JavaThread "Reference Handler" daemon [_thread_blocked, id=40944, stack(0x000000ccd6100000,0x000000ccd6200000)]
  0x000002aa65983a50 JavaThread "Finalizer" daemon [_thread_blocked, id=15412, stack(0x000000ccd6200000,0x000000ccd6300000)]
  0x000002aa65999950 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=38100, stack(0x000000ccd6300000,0x000000ccd6400000)]
  0x000002aa6599c230 JavaThread "Attach Listener" daemon [_thread_blocked, id=38096, stack(0x000000ccd6400000,0x000000ccd6500000)]
  0x000002aa6599cb00 JavaThread "Service Thread" daemon [_thread_blocked, id=42312, stack(0x000000ccd6500000,0x000000ccd6600000)]
  0x000002aa6599d3d0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=8148, stack(0x000000ccd6600000,0x000000ccd6700000)]
=>0x000002aa6599dfd0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=35932, stack(0x000000ccd6700000,0x000000ccd6800000)]
  0x000002aa6599e8e0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=38656, stack(0x000000ccd6800000,0x000000ccd6900000)]
  0x000002aa65ab1b50 JavaThread "Sweeper thread" daemon [_thread_blocked, id=38276, stack(0x000000ccd6900000,0x000000ccd6a00000)]
  0x000002aa65b1d070 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=52264, stack(0x000000ccd6a00000,0x000000ccd6b00000)]
  0x000002aa65b2b7e0 JavaThread "Notification Thread" daemon [_thread_blocked, id=52268, stack(0x000000ccd6b00000,0x000000ccd6c00000)]
  0x000002aa6accb500 JavaThread "Daemon health stats" [_thread_blocked, id=54576, stack(0x000000ccd5700000,0x000000ccd5800000)]
  0x000002aa6b2d0040 JavaThread "Incoming local TCP Connector on port 51263" [_thread_in_native, id=11564, stack(0x000000ccd7200000,0x000000ccd7300000)]
  0x000002aa6ab20ea0 JavaThread "Daemon periodic checks" [_thread_in_native, id=3192, stack(0x000000ccd7300000,0x000000ccd7400000)]
  0x000002aa65d525b0 JavaThread "Daemon" [_thread_blocked, id=51636, stack(0x000000ccd7400000,0x000000ccd7500000)]
  0x000002aa6b248420 JavaThread "Handler for socket connection from /127.0.0.1:51263 to /127.0.0.1:51319" [_thread_in_native, id=52984, stack(0x000000ccd7500000,0x000000ccd7600000)]
  0x000002aa65d22a70 JavaThread "Cancel handler" [_thread_blocked, id=36304, stack(0x000000ccd7600000,0x000000ccd7700000)]
  0x000002aa65d5df20 JavaThread "Daemon worker" [_thread_in_Java, id=47144, stack(0x000000ccd7900000,0x000000ccd7a00000)]
  0x000002aa65d5fd80 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51263 to /127.0.0.1:51319" [_thread_blocked, id=38496, stack(0x000000ccd7a00000,0x000000ccd7b00000)]
  0x000002aa65d5e430 JavaThread "Stdin handler" [_thread_blocked, id=54596, stack(0x000000ccd7b00000,0x000000ccd7c00000)]
  0x000002aa65d60290 JavaThread "Daemon client event forwarder" [_thread_blocked, id=29784, stack(0x000000ccd7c00000,0x000000ccd7d00000)]

Other Threads:
  0x000002aa6597af60 VMThread "VM Thread" [stack: 0x000000ccd6000000,0x000000ccd6100000] [id=51728]
  0x000002aa65b2dcd0 WatcherThread [stack: 0x000000ccd6c00000,0x000000ccd6d00000] [id=52272]
  0x000002aa47334f90 GCTaskThread "GC Thread#0" [stack: 0x000000ccd5b00000,0x000000ccd5c00000] [id=51540]
  0x000002aa65d0e310 GCTaskThread "GC Thread#1" [stack: 0x000000ccd6d00000,0x000000ccd6e00000] [id=54028]
  0x000002aa65d0e5d0 GCTaskThread "GC Thread#2" [stack: 0x000000ccd6e00000,0x000000ccd6f00000] [id=54032]
  0x000002aa65d0e890 GCTaskThread "GC Thread#3" [stack: 0x000000ccd6f00000,0x000000ccd7000000] [id=54096]
  0x000002aa65d0eb50 GCTaskThread "GC Thread#4" [stack: 0x000000ccd7000000,0x000000ccd7100000] [id=54100]
  0x000002aa65d0ee10 GCTaskThread "GC Thread#5" [stack: 0x000000ccd7100000,0x000000ccd7200000] [id=54184]
  0x000002aa47347e10 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000ccd5c00000,0x000000ccd5d00000] [id=51544]
  0x000002aa47348730 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000ccd5d00000,0x000000ccd5e00000] [id=51548]
  0x000002aa4739ebe0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000ccd5e00000,0x000000ccd5f00000] [id=51552]
  0x000002aa6582d680 ConcurrentGCThread "G1 Service" [stack: 0x000000ccd5f00000,0x000000ccd6000000] [id=51556]

Threads with active compile tasks:
C2 CompilerThread0    98132 2206       4       jdk.internal.org.objectweb.asm.Frame::execute (2305 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002aa00000000-0x000002aa00bd0000-0x000002aa00bd0000), size 12386304, SharedBaseAddress: 0x000002aa00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002aa01000000-0x000002aa1b000000, reserved size: 436207616
Narrow klass base: 0x000002aa00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 56492K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 47 young (48128K), 2 survivors (2048K)
 Metaspace       used 12337K, committed 12608K, reserved 491520K
  class space    used 1708K, committed 1856K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000, 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000, 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000, 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080700000, 0x0000000080800000|  0%| F|  |TAMS 0x0000000080700000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080ae5600, 0x0000000080b00000| 89%| O|  |TAMS 0x0000000080a00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080b00000, 0x0000000080c00000|  0%| F|  |TAMS 0x0000000080b00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080c00000, 0x0000000080d00000|  0%| F|  |TAMS 0x0000000080c00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080d00000, 0x0000000080e00000|  0%| F|  |TAMS 0x0000000080d00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080e00000, 0x0000000080f00000|  0%| F|  |TAMS 0x0000000080e00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000080f00000, 0x0000000081000000|  0%| F|  |TAMS 0x0000000080f00000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081000000, 0x0000000081100000|  0%| F|  |TAMS 0x0000000081000000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081100000, 0x0000000081200000|  0%| F|  |TAMS 0x0000000081100000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081200000, 0x0000000081300000|  0%| F|  |TAMS 0x0000000081200000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081300000, 0x0000000081400000|  0%| F|  |TAMS 0x0000000081300000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081400000, 0x0000000081500000|  0%| F|  |TAMS 0x0000000081400000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081500000, 0x0000000081600000|  0%| F|  |TAMS 0x0000000081500000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081600000, 0x0000000081700000|  0%| F|  |TAMS 0x0000000081600000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081700000, 0x0000000081800000|  0%| F|  |TAMS 0x0000000081700000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082400000, 0x0000000082500000|  0%| F|  |TAMS 0x0000000082400000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082500000, 0x0000000082600000|  0%| F|  |TAMS 0x0000000082500000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082600000, 0x0000000082700000|  0%| F|  |TAMS 0x0000000082600000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082700000, 0x0000000082800000|  0%| F|  |TAMS 0x0000000082700000, 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082800000, 0x0000000082900000|  0%| F|  |TAMS 0x0000000082800000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082900000, 0x0000000082a00000|  0%| F|  |TAMS 0x0000000082900000, 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087000000, 0x0000000087100000|  0%| F|  |TAMS 0x0000000087000000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087100000, 0x0000000087200000|  0%| F|  |TAMS 0x0000000087100000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087900000, 0x0000000087a00000|  0%| F|  |TAMS 0x0000000087900000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087e00000, 0x0000000087f00000|  0%| F|  |TAMS 0x0000000087e00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088400000, 0x0000000088500000|  0%| F|  |TAMS 0x0000000088400000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000, 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000, 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000, 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000, 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000, 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000, 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf80800, 0x000000008d000000| 50%| E|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000, 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000, 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e045bc0, 0x000000008e100000| 27%| S|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| S|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000, 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000, 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000, 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000, 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000, 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000, 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000, 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000, 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000, 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000, 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000, 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000, 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x000002aa5e4f0000,0x000002aa5e8f0000] _byte_map_base: 0x000002aa5e0f0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002aa473364d0, (CMBitMap*) 0x000002aa47336510
 Prev Bits: [0x000002aa5ecf0000, 0x000002aa60cf0000)
 Next Bits: [0x000002aa60cf0000, 0x000002aa62cf0000)

Polling page: 0x000002aa469b0000

Metaspace:

Usage:
  Non-class:     10.39 MB used.
      Class:      1.67 MB used.
       Both:     12.06 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      10.56 MB ( 17%) committed,  1 nodes.
      Class space:      416.00 MB reserved,       1.81 MB ( <1%) committed,  1 nodes.
             Both:      480.00 MB reserved,      12.38 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  5.48 MB
       Class:  14.16 MB
        Both:  19.64 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 178.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 198.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 530.
num_chunk_merges: 0.
num_chunk_splits: 359.
num_chunks_enlarged: 277.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1024Kb max_used=1024Kb free=118975Kb
 bounds [0x000002aa56660000, 0x000002aa568d0000, 0x000002aa5db90000]
CodeHeap 'profiled nmethods': size=120000Kb used=4619Kb max_used=4619Kb free=115380Kb
 bounds [0x000002aa4eb90000, 0x000002aa4f020000, 0x000002aa560c0000]
CodeHeap 'non-nmethods': size=5760Kb used=1210Kb max_used=1227Kb free=4549Kb
 bounds [0x000002aa560c0000, 0x000002aa56330000, 0x000002aa56660000]
 total_blobs=2765 nmethods=2252 adapters=425
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 96.801 Thread 0x000002aa6599e8e0 2230       3       java.util.ArrayList::toArray (57 bytes)
Event: 96.801 Thread 0x000002aa6599e8e0 nmethod 2230 0x000002aa4efff310 code [0x000002aa4efff4e0, 0x000002aa4efffa58]
Event: 96.870 Thread 0x000002aa6599e8e0 2231       3       sun.reflect.generics.visitor.Reifier::getResult (26 bytes)
Event: 96.870 Thread 0x000002aa6599e8e0 nmethod 2231 0x000002aa4efffb90 code [0x000002aa4efffd20, 0x000002aa4efffe38]
Event: 96.880 Thread 0x000002aa6599e8e0 2232       3       java.lang.invoke.DirectMethodHandle::make (275 bytes)
Event: 96.881 Thread 0x000002aa6599e8e0 nmethod 2232 0x000002aa4effff10 code [0x000002aa4f0002a0, 0x000002aa4f001738]
Event: 96.881 Thread 0x000002aa6599e8e0 2233       3       java.lang.invoke.MemberName::isField (8 bytes)
Event: 96.881 Thread 0x000002aa6599e8e0 nmethod 2233 0x000002aa4f001f90 code [0x000002aa4f002140, 0x000002aa4f002378]
Event: 96.883 Thread 0x000002aa6599e8e0 2235       3       java.nio.HeapByteBuffer::get (14 bytes)
Event: 96.883 Thread 0x000002aa6599e8e0 nmethod 2235 0x000002aa4f002490 code [0x000002aa4f002660, 0x000002aa4f002948]
Event: 96.898 Thread 0x000002aa6599e8e0 2236   !   3       java.lang.invoke.MethodHandle::setVarargs (25 bytes)
Event: 96.898 Thread 0x000002aa6599e8e0 nmethod 2236 0x000002aa4f002b10 code [0x000002aa4f002d20, 0x000002aa4f0033d8]
Event: 96.898 Thread 0x000002aa6599e8e0 2237       3       java.lang.invoke.MemberName::isVarargs (23 bytes)
Event: 96.899 Thread 0x000002aa6599e8e0 nmethod 2237 0x000002aa4f003610 code [0x000002aa4f0037e0, 0x000002aa4f003c78]
Event: 96.936 Thread 0x000002aa6599e8e0 2238       3       java.util.concurrent.TimeUnit::toNanos (45 bytes)
Event: 96.936 Thread 0x000002aa6599e8e0 nmethod 2238 0x000002aa4f003e10 code [0x000002aa4f003fc0, 0x000002aa4f004238]
Event: 96.970 Thread 0x000002aa6599e8e0 2239       3       org.gradle.internal.service.DefaultServiceRegistry$FactoryService::<init> (7 bytes)
Event: 96.971 Thread 0x000002aa6599e8e0 nmethod 2239 0x000002aa4f004310 code [0x000002aa4f004520, 0x000002aa4f004d88]
Event: 97.403 Thread 0x000002aa6599e8e0 2240       3       java.util.stream.AbstractPipeline::<init> (91 bytes)
Event: 97.403 Thread 0x000002aa6599e8e0 nmethod 2240 0x000002aa4f005010 code [0x000002aa4f005200, 0x000002aa4f005878]

GC Heap History (4 events):
Event: 21.341 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 26624K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 2997K, committed 3136K, reserved 491520K
  class space    used 380K, committed 448K, reserved 425984K
}
Event: 23.455 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 10119K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 2997K, committed 3136K, reserved 491520K
  class space    used 380K, committed 448K, reserved 425984K
}
Event: 35.693 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 35719K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 3 survivors (3072K)
 Metaspace       used 3898K, committed 4096K, reserved 491520K
  class space    used 475K, committed 576K, reserved 425984K
}
Event: 35.696 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 11436K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 3898K, committed 4096K, reserved 491520K
  class space    used 475K, committed 576K, reserved 425984K
}

Deoptimization events (20 events):
Event: 95.731 Thread 0x000002aa65d5df20 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002aa5671c614 relative=0x00000000000016b4
Event: 95.731 Thread 0x000002aa65d5df20 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002aa5671c614 method=java.util.concurrent.ConcurrentHashMap.transfer([Ljava/util/concurrent/ConcurrentHashMap$Node;[Ljava/util/concurrent/ConcurrentHashMap$Node;)V @ 26 c2
Event: 95.731 Thread 0x000002aa65d5df20 DEOPT PACKING pc=0x000002aa5671c614 sp=0x000000ccd79f9450
Event: 95.731 Thread 0x000002aa65d5df20 DEOPT UNPACKING pc=0x000002aa561123a3 sp=0x000000ccd79f93c0 mode 2
Event: 96.310 Thread 0x000002aa65d5df20 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002aa5669b210 relative=0x0000000000000250
Event: 96.311 Thread 0x000002aa65d5df20 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002aa5669b210 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 66 c2
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT PACKING pc=0x000002aa5669b210 sp=0x000000ccd79fc6c0
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT UNPACKING pc=0x000002aa561123a3 sp=0x000000ccd79fc640 mode 2
Event: 96.311 Thread 0x000002aa65d5df20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002aa567510cc relative=0x0000000000001b8c
Event: 96.311 Thread 0x000002aa65d5df20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002aa567510cc method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT PACKING pc=0x000002aa567510cc sp=0x000000ccd79fc680
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT UNPACKING pc=0x000002aa561123a3 sp=0x000000ccd79fc620 mode 2
Event: 96.311 Thread 0x000002aa65d5df20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002aa567510cc relative=0x0000000000001b8c
Event: 96.311 Thread 0x000002aa65d5df20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002aa567510cc method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT PACKING pc=0x000002aa567510cc sp=0x000000ccd79fc680
Event: 96.311 Thread 0x000002aa65d5df20 DEOPT UNPACKING pc=0x000002aa561123a3 sp=0x000000ccd79fc620 mode 2
Event: 96.347 Thread 0x000002aa65d5df20 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002aa56706664 relative=0x0000000000000424
Event: 96.347 Thread 0x000002aa65d5df20 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002aa56706664 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 152 c2
Event: 96.347 Thread 0x000002aa65d5df20 DEOPT PACKING pc=0x000002aa56706664 sp=0x000000ccd79fbb70
Event: 96.347 Thread 0x000002aa65d5df20 DEOPT UNPACKING pc=0x000002aa561123a3 sp=0x000000ccd79fbae8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 16.585 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ecbb8a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008ecbb8a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 16.590 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008eccf178}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008eccf178) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 24.622 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008fa44b80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008fa44b80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 39.444 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ed2d548}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x000000008ed2d548) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 40.035 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ed89958}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000008ed89958) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 42.166 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e885da0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x000000008e885da0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 44.478 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e76cde8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x000000008e76cde8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 47.904 Thread 0x000002aa472d8ab0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e6cb518}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008e6cb518) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 55.402 Thread 0x000002aa472d8ab0 Exception <a 'java/io/IOException'{0x000000008e59dde0}> (0x000000008e59dde0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 516]
Event: 95.949 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dcc4020}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000008dcc4020) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 95.966 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008db05f10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008db05f10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.336 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d7a17f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d7a17f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.458 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d547988}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d547988) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.524 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d5c8688}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d5c8688) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.528 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d5d5688}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d5d5688) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.755 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d260040}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d260040) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.757 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d268b40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d268b40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.758 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d26d608}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d26d608) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.759 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d273400}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d273400) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 96.880 Thread 0x000002aa65d5df20 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d1811e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008d1811e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 66.063 Executing VM operation: Cleanup
Event: 66.064 Executing VM operation: Cleanup done
Event: 67.079 Executing VM operation: Cleanup
Event: 67.079 Executing VM operation: Cleanup done
Event: 79.190 Executing VM operation: Cleanup
Event: 79.190 Executing VM operation: Cleanup done
Event: 85.264 Executing VM operation: Cleanup
Event: 85.265 Executing VM operation: Cleanup done
Event: 86.278 Executing VM operation: Cleanup
Event: 86.278 Executing VM operation: Cleanup done
Event: 88.304 Executing VM operation: Cleanup
Event: 88.305 Executing VM operation: Cleanup done
Event: 89.313 Executing VM operation: Cleanup
Event: 89.313 Executing VM operation: Cleanup done
Event: 95.391 Executing VM operation: Cleanup
Event: 95.392 Executing VM operation: Cleanup done
Event: 96.402 Executing VM operation: Cleanup
Event: 96.402 Executing VM operation: Cleanup done
Event: 97.416 Executing VM operation: Cleanup
Event: 97.417 Executing VM operation: Cleanup done

Events (20 events):
Event: 96.346 loading class jdk/internal/vm/annotation/IntrinsicCandidate
Event: 96.346 loading class jdk/internal/vm/annotation/IntrinsicCandidate done
Event: 96.350 loading class java/util/stream/MatchOps$MatchKind
Event: 96.350 loading class java/util/stream/MatchOps$MatchKind done
Event: 96.350 loading class java/util/stream/MatchOps
Event: 96.350 loading class java/util/stream/MatchOps done
Event: 96.350 loading class java/util/stream/MatchOps$MatchOp
Event: 96.350 loading class java/util/stream/MatchOps$MatchOp done
Event: 96.353 loading class java/util/stream/MatchOps$BooleanTerminalSink
Event: 96.355 loading class java/util/stream/MatchOps$BooleanTerminalSink done
Event: 96.355 loading class java/util/stream/MatchOps$1MatchSink
Event: 96.355 loading class java/util/stream/MatchOps$1MatchSink done
Event: 96.554 loading class java/lang/invoke/MethodHandle$1
Event: 96.559 loading class java/lang/invoke/MethodHandle$1 done
Event: 96.577 loading class java/util/ConcurrentModificationException
Event: 96.581 loading class java/util/ConcurrentModificationException done
Event: 96.696 loading class sun/reflect/generics/scope/ConstructorScope
Event: 96.696 loading class sun/reflect/generics/scope/ConstructorScope done
Event: 96.696 loading class sun/reflect/generics/tree/VoidDescriptor
Event: 96.696 loading class sun/reflect/generics/tree/VoidDescriptor done


Dynamic libraries:
0x00007ff6208b0000 - 0x00007ff6208c0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007fffa6c00000 - 0x00007fffa6e65000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffa49a0000 - 0x00007fffa4a69000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffa40e0000 - 0x00007fffa44c8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffa3f90000 - 0x00007fffa40db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff9cb70000 - 0x00007fff9cb89000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007fffa6b00000 - 0x00007fffa6bb3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffa6070000 - 0x00007fffa6119000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fffa6a00000 - 0x00007fffa6aa6000 	C:\WINDOWS\System32\sechost.dll
0x00007fffa57f0000 - 0x00007fffa5905000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffa5350000 - 0x00007fffa551a000 	C:\WINDOWS\System32\USER32.dll
0x00007fffa3ed0000 - 0x00007fffa3ef7000 	C:\WINDOWS\System32\win32u.dll
0x00007fffa4d70000 - 0x00007fffa4d9b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffa4580000 - 0x00007fffa46b7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffa44d0000 - 0x00007fffa4573000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff9c920000 - 0x00007fff9c93b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007fff9be00000 - 0x00007fff9be0b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff8ec00000 - 0x00007fff8ee9a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fffa4da0000 - 0x00007fffa4dd0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff9e700000 - 0x00007fff9e70c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007fff68070000 - 0x00007fff680fe000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007fff30bd0000 - 0x00007fff317b0000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007fffa4a70000 - 0x00007fffa4a78000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff89d80000 - 0x00007fff89d8a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fffa6120000 - 0x00007fffa6194000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff97df0000 - 0x00007fff97e25000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffa2c10000 - 0x00007fffa2c2b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff9c910000 - 0x00007fff9c91a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007fffa1570000 - 0x00007fffa17b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffa4fc0000 - 0x00007fffa5345000 	C:\WINDOWS\System32\combase.dll
0x00007fffa55c0000 - 0x00007fffa56a1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff8da10000 - 0x00007fff8da49000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffa4780000 - 0x00007fffa4819000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff5db60000 - 0x00007fff5db85000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffebf6a0000 - 0x00007ffebf777000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007fffa61a0000 - 0x00007fffa68e2000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffa4820000 - 0x00007fffa4994000 	C:\WINDOWS\System32\wintypes.dll
0x00007fffa1970000 - 0x00007fffa21c8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffa4a80000 - 0x00007fffa4b71000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffa5da0000 - 0x00007fffa5e0a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffa3c60000 - 0x00007fffa3c8f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff5db40000 - 0x00007fff5db59000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007fff9c0f0000 - 0x00007fff9c20e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffa31a0000 - 0x00007fffa320a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff3d6e0000 - 0x00007fff3d6f6000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007fff30bb0000 - 0x00007fff30bc8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007fff30ba0000 - 0x00007fff30bb0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007fff90860000 - 0x00007fff90887000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007ffeb12c0000 - 0x00007ffeb1404000 	C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
0x00007fff68060000 - 0x00007fff6806a000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007fff30b90000 - 0x00007fff30b9b000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007fffa3450000 - 0x00007fffa346b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffa2b70000 - 0x00007fffa2baa000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffa3240000 - 0x00007fffa326b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffa3c30000 - 0x00007fffa3c56000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fffa3470000 - 0x00007fffa347c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffa25d0000 - 0x00007fffa2603000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffa68f0000 - 0x00007fffa68fa000 	C:\WINDOWS\System32\NSI.dll
0x00007fff9c660000 - 0x00007fff9c67f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff9c410000 - 0x00007fff9c435000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffa2670000 - 0x00007fffa2797000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fff30b80000 - 0x00007fff30b8e000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007fffa3d50000 - 0x00007fffa3ec7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fffa3670000 - 0x00007fffa36a0000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fffa3620000 - 0x00007fffa365f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff40420000 - 0x00007fff40428000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64;C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.0.1-all\aro4hu1c3oeioove7l0i4i14o\gradle-8.0.1\lib\gradle-launcher-8.0.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
CLASSPATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;%JAVA_HOME%\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\wamp64\bin\php\php8.1.13;C:\ProgramData\ComposerSetup\bin;C:\Program Files\RabbitMQ Server\rabbitmq_server-3.12.2\sbin;C:\Program Files\MongoDB\Tools\100\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\cloudflared\;c:\users\<USER>\pear.bat;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Progr
USERNAME=MSI
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 1 days 5:40 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16228M (617M free)
TotalPageFile size 62338M (AvailPageFile size 18M)
current process WorkingSet (physical memory assigned to process): 83M, peak: 90M
current process commit charge ("private bytes"): 398M, peak: 401M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207) for windows-amd64 JRE (17.0.11+7-LTS-207), built on Mar 11 2024 19:01:50 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
