/**
 * Service Settings pour Mobile E-Luxe 1.0
 * Synchronisé avec le client web E-Luxe
 * Gère les paramètres globaux, logo, slider, etc.
 */

import { apiClient } from '../data/datasources/ApiClient';

// Types pour les settings (synchronisés avec le client web)
export interface GlobalSetting {
  shop_name: string;
  address: string;
  company_name: string;
  contact: string;
  email: string;
  website: string;
  default_currency: string;
  default_time_zone: string;
  default_date_format: string;
  logo?: string;
  phone?: string;
  default_product_image?: string;
}

export interface SliderSetting {
  // Structure EXACTE comme le client web (settings.json)
  left_right_arrow?: boolean;
  bottom_dots?: boolean;
  both_slider?: boolean;

  // Premier slide
  first_img: string;
  first_title: Record<string, string>; // Multilingue {en, fr, de}
  first_description: Record<string, string>; // Multilingue
  first_button: Record<string, string>; // Multilingue
  first_link?: string;

  // Deuxième slide
  second_img: string;
  second_title: Record<string, string>; // Multilingue
  second_description: Record<string, string>; // Multilingue
  second_button: Record<string, string>; // Multilingue
  second_link?: string;

  // Troisième slide (optionnel)
  third_img?: string;
  third_title?: Record<string, string>; // Multilingue
  third_description?: Record<string, string>; // Multilingue
  third_button?: Record<string, string>; // Multilingue
  third_link?: string;
}

export interface NavbarSetting {
  logo: string;
  menu_items?: any[];
}

export interface StoreCustomizationSetting {
  navbar: NavbarSetting;
  slider: SliderSetting;
  offers?: {
    header_status: boolean;
    header_bg: string;
    title: Record<string, string>;
    coupon_code: string[];
  };
  privacy_policy?: any;
  terms_conditions?: any;
  about_us?: any;
  contact_us?: any;
}

export interface SettingsResponse {
  globalSetting: GlobalSetting;
  storeCustomizationSetting: StoreCustomizationSetting;
}

/**
 * Service Settings
 * Utilise la même architecture que l'application web client-e-luxe
 */
export class SettingsService {
  
  /**
   * Récupère les paramètres globaux - Endpoint API Gateway confirmé
   */
  async getGlobalSettings() {
    console.log('⚙️ SettingsService.getGlobalSettings');
    try {
      // Endpoint confirmé de l'API Gateway: /v1/{lang}/setting/global
      const response = await apiClient.getWithLang<GlobalSetting>('/v1/{lang}/setting/global');
      return response?.data || response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des paramètres globaux:', error);
      // Fallback vers des paramètres par défaut
      return this.getGlobalSettings();
    }
  }

  /**
   * Récupère les paramètres de personnalisation du magasin - Endpoint API Gateway confirmé
   */
  async getStoreCustomizationSettings() {
    console.log('🎨 SettingsService.getStoreCustomizationSettings');
    try {
      // Endpoint confirmé de l'API Gateway: /v1/{lang}/setting/store/customization
      const response = await apiClient.getWithLang<StoreCustomizationSetting>('/v1/{lang}/setting/store/customization');
      return response?.data || response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des paramètres de personnalisation:', error);
      // Fallback vers des paramètres par défaut
      return this.getStoreCustomizationSettings();
    }
  }

  /**
   * Récupère tous les paramètres en une seule fois - comme le client web
   */
  async getAllSettings(): Promise<SettingsResponse> {
    console.log('📋 SettingsService.getAllSettings');
    try {
      const [globalSetting, storeCustomizationSetting] = await Promise.all([
        this.getGlobalSettings(),
        this.getStoreCustomizationSettings(),
      ]);

      return {
        globalSetting,
        storeCustomizationSetting,
      };
    } catch (error) {
      console.error('❌ Erreur lors de la récupération de tous les paramètres:', error);
      // Fallback vers des paramètres par défaut
      return {
        globalSetting: await this.getGlobalSettings(),
        storeCustomizationSetting: await this.getStoreCustomizationSettings(),
      };
    }
  }

  /**
   * Récupère le logo du magasin
   */
  async getLogo(): Promise<string> {
    console.log('🏷️ SettingsService.getLogo');
    try {
      const settings = await this.getStoreCustomizationSettings();
      return settings.navbar?.logo || 'https://via.placeholder.com/150x50/D4AF37/000000?text=E-LUXE';
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du logo:', error);
      return 'https://via.placeholder.com/150x50/D4AF37/000000?text=E-LUXE';
    }
  }

  /**
   * Récupère les slides du slider - comme le client web
   */
  async getSliderSettings(): Promise<SliderSetting> {
    console.log('🎠 SettingsService.getSliderSettings');
    try {
      const settings = await this.getStoreCustomizationSettings();
      return settings.slider || await this.getSliderSettings();
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des slides:', error);
      return {
        slides: [],
        autoplay: true,
        interval: 5000,
        showDots: true,
        showArrows: true,
      };
    }
  }

}

// Instance singleton
export const settingsService = new SettingsService();
