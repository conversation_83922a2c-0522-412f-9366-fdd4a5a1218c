import { AuthRepository } from '../../repositories/AuthRepository';
import { RegisterData, AuthUser } from '../../entities/User';

export class RegisterUseCase {
  constructor(private authRepository: AuthRepository) {}

  async execute(data: RegisterData): Promise<AuthUser> {
    // Validate input
    this.validateRegistrationData(data);

    try {
      const authUser = await this.authRepository.register(data);
      
      // Auto-store token after successful registration
      await this.authRepository.storeToken(authUser.token);

      return authUser;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Registration failed: ${error.message}`);
      }
      throw new Error('Registration failed: Unknown error');
    }
  }

  private validateRegistrationData(data: RegisterData): void {
    if (!data.email || !data.password || !data.confirmPassword) {
      throw new Error('Email, password, and password confirmation are required');
    }

    if (!this.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (data.password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    if (data.password !== data.confirmPassword) {
      throw new Error('Passwords do not match');
    }

    if (!this.isStrongPassword(data.password)) {
      throw new Error('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
    }

    if (!data.acceptTerms) {
      throw new Error('You must accept the terms and conditions');
    }

    if (data.phone && !this.isValidPhone(data.phone)) {
      throw new Error('Invalid phone number format');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isStrongPassword(password: string): boolean {
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
    return strongPasswordRegex.test(password);
  }

  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }
}
