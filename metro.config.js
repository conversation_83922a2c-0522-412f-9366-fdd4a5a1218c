const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  server: {
    port: 8081,
    enhanceMiddleware: (middleware) => {
      return (req, res, next) => {
        // Ajouter les headers CORS pour éviter les erreurs WebSocket
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        return middleware(req, res, next);
      };
    },
  },
  resolver: {
    alias: {
      '@': './src',
      '@/components': './src/presentation/components',
      '@/screens': './src/presentation/screens',
      '@/navigation': './src/presentation/navigation',
      '@/hooks': './src/presentation/hooks',
      '@/store': './src/presentation/store',
      '@/domain': './src/domain',
      '@/data': './src/data',
      '@/utils': './src/utils',
      '@/types': './src/types',
      '@/constants': './src/constants',
    },
  },
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  watchFolders: [],
  watcher: {
    ignored: [
      // Ignorer les dossiers build Android problématiques
      /node_modules\/.*\/android\/build\/.*/,
      /node_modules\/.*\/android\/\.gradle\/.*/,
      /node_modules\/.*\/android\/gradle\/.*/,
      // Ignorer les caches Kotlin spécifiquement
      /node_modules\/.*\/android\/build\/kotlin\/.*/,
      /node_modules\/.*\/android\/build\/.*\/kotlin\/.*/,
      // Ignorer les autres dossiers temporaires
      /node_modules\/.*\/\.gradle\/.*/,
      /android\/build\/.*/,
      /android\/\.gradle\/.*/,
      // Ignorer les caches Metro
      /\.metro-cache\/.*/,
      /metro-cache\/.*/,
    ],
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
