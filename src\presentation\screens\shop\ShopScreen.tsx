/**
 * ShopScreen - Mobile E-Luxe 1.0
 * Reproduction exacte de la page Shop du client web
 * Avec filtres, tri, pagination et grille de produits
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { getProductsAsync, setFilter, clearProducts, getCategoriesAsync } from '../../store/slices/productSlice';
import { addToCartLocal } from '../../store/slices/cartSlice';
import ProductCard from '../../components/ProductCard';
import { ELuxeColors, ComponentColors } from '../../../theme/colors';
import { TextStyles } from '../../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ShopScreenProps {}

interface FilterState {
  category?: string;
  priceRange?: { min: number; max: number };
  rating?: number;
  sortBy?: 'price_asc' | 'price_desc' | 'rating' | 'newest' | 'popular';
  inStock?: boolean;
}

const ShopScreen: React.FC<ShopScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  
  const { products, isLoading, filter } = useAppSelector((state) => state.product);
  const { categories } = useAppSelector((state) => state.product);
  
  const [localFilter, setLocalFilter] = useState<FilterState>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const PRODUCTS_PER_PAGE = 48; // 48 produits par page comme demandé

  // Initialisation avec les paramètres de route
  useEffect(() => {
    // Charger les catégories pour les filtres
    dispatch(getCategoriesAsync());

    // Récupérer les paramètres de route (ex: categoryId depuis navigation)
    const routeParams = route.params as any;
    if (routeParams?.categoryId) {
      setLocalFilter(prev => ({ ...prev, category: routeParams.categoryId }));
    }
    if (routeParams?.filters) {
      setLocalFilter(prev => ({ ...prev, ...routeParams.filters }));
    }
  }, []);

  useEffect(() => {
    loadProducts();
  }, [localFilter]);

  const loadProducts = async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) {
        setCurrentPage(1);
        dispatch(clearProducts()); // Clear existing products for new search
      }

      console.log('🛍️ ShopScreen - Loading products with filters:', {
        category: localFilter.category,
        sortBy: localFilter.sortBy,
        page: page,
        limit: PRODUCTS_PER_PAGE
      });

      const response = await dispatch(getProductsAsync({
        category: localFilter.category,
        sortBy: localFilter.sortBy,
        minPrice: localFilter.priceRange?.min,
        maxPrice: localFilter.priceRange?.max,
        rating: localFilter.rating,
        inStock: localFilter.inStock,
        page: page,
        limit: PRODUCTS_PER_PAGE,
      })).unwrap();

      console.log('✅ ShopScreen - Products loaded:', response);

      // Mettre à jour les informations de pagination
      if (response?.pagination) {
        setTotalPages(response.pagination.totalPages || 1);
        setTotalProducts(response.pagination.total || 0);
        setCurrentPage(page);
      } else if (response?.products) {
        // Fallback si pas de pagination dans la réponse
        setTotalProducts(response.products.length);
        setCurrentPage(page);
      }

    } catch (error) {
      console.error('❌ ShopScreen - Error loading products:', error);
    }
  };

  const handleProductPress = (product: any) => {
    navigation.navigate('ProductDetail', { productId: product.id || product._id });
  };

  const handleAddToCart = (product: any) => {
    try {
      dispatch(addToCartLocal({
        id: product.id || product._id,
        productId: product.id || product._id,
        title: product.title || product.name,
        price: product.price || product.prices?.price || 0,
        image: product.image || product.images?.[0] || '',
        quantity: 1,
        variant: product.variants?.[0] || null,
      }));

      // TODO: Afficher un toast de confirmation
    } catch (error) {
      // Erreur silencieuse
    }
  };

  const handleFilterChange = (newFilter: Partial<FilterState>) => {
    setLocalFilter(prev => ({ ...prev, ...newFilter }));
  };

  const clearAllFilters = () => {
    setLocalFilter({});
    setCurrentPage(1);
    dispatch(clearProducts());
  };

  const loadMoreProducts = async () => {
    if (isLoadingMore || currentPage >= totalPages) return;

    setIsLoadingMore(true);
    try {
      await loadProducts(currentPage + 1, true);
      setCurrentPage(prev => prev + 1);
    } catch (error) {
      // Erreur silencieuse
    } finally {
      setIsLoadingMore(false);
    }
  };

  const renderFilterModal = () => (
    <Modal
      visible={showFilters}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowFilters(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Filters</Text>
          <TouchableOpacity onPress={() => setShowFilters(false)}>
            <Icon name="close" size={24} color={ELuxeColors.textPrimary} />
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.modalContent}>
          {/* Categories Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterTitle}>Categories</Text>
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id || category._id}
                style={[
                  styles.filterOption,
                  localFilter.category === category.id && styles.filterOptionActive
                ]}
                onPress={() => handleFilterChange({ category: category.id })}
              >
                <Text style={[
                  styles.filterOptionText,
                  localFilter.category === category.id && styles.filterOptionTextActive
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Price Range Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterTitle}>Price Range</Text>
            {[
              { label: 'Under $50', min: 0, max: 50 },
              { label: '$50 - $100', min: 50, max: 100 },
              { label: '$100 - $200', min: 100, max: 200 },
              { label: 'Over $200', min: 200, max: 9999 },
            ].map((range) => (
              <TouchableOpacity
                key={range.label}
                style={[
                  styles.filterOption,
                  localFilter.priceRange?.min === range.min && styles.filterOptionActive
                ]}
                onPress={() => handleFilterChange({ priceRange: { min: range.min, max: range.max } })}
              >
                <Text style={[
                  styles.filterOptionText,
                  localFilter.priceRange?.min === range.min && styles.filterOptionTextActive
                ]}>
                  {range.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Rating Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterTitle}>Rating</Text>
            {[5, 4, 3, 2, 1].map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[
                  styles.filterOption,
                  localFilter.rating === rating && styles.filterOptionActive
                ]}
                onPress={() => handleFilterChange({ rating })}
              >
                <Text style={[
                  styles.filterOptionText,
                  localFilter.rating === rating && styles.filterOptionTextActive
                ]}>
                  {rating} Stars & Up
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        <View style={styles.modalFooter}>
          <TouchableOpacity style={styles.clearButton} onPress={clearAllFilters}>
            <Text style={styles.clearButtonText}>Clear All</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.applyButton} onPress={() => setShowFilters(false)}>
            <Text style={styles.applyButtonText}>Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  const renderSortModal = () => (
    <Modal
      visible={showSortModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowSortModal(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Sort By</Text>
          <TouchableOpacity onPress={() => setShowSortModal(false)}>
            <Icon name="close" size={24} color={ELuxeColors.textPrimary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.modalContent}>
          {[
            { key: 'popular', label: 'Most Popular' },
            { key: 'newest', label: 'Newest First' },
            { key: 'price_asc', label: 'Price: Low to High' },
            { key: 'price_desc', label: 'Price: High to Low' },
            { key: 'rating', label: 'Highest Rated' },
          ].map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[
                styles.sortOption,
                localFilter.sortBy === option.key && styles.sortOptionActive
              ]}
              onPress={() => {
                handleFilterChange({ sortBy: option.key as any });
                setShowSortModal(false);
              }}
            >
              <Text style={[
                styles.sortOptionText,
                localFilter.sortBy === option.key && styles.sortOptionTextActive
              ]}>
                {option.label}
              </Text>
              {localFilter.sortBy === option.key && (
                <Icon name="check" size={20} color={ELuxeColors.primary} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </SafeAreaView>
    </Modal>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.resultCount}>
          {totalProducts > 0 ? `${totalProducts} products found` : `${products.length} products`}
          {totalPages > 1 && ` (Page ${currentPage} of ${totalPages})`}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.viewModeButton}
            onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            <Icon 
              name={viewMode === 'grid' ? 'view-list' : 'view-module'} 
              size={20} 
              color={ELuxeColors.textPrimary} 
            />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.headerBottom}>
        <TouchableOpacity style={styles.filterButton} onPress={() => setShowFilters(true)}>
          <Icon name="filter-list" size={20} color={ELuxeColors.white} />
          <Text style={styles.filterButtonText}>Filters</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.sortButton} onPress={() => setShowSortModal(true)}>
          <Icon name="sort" size={20} color={ELuxeColors.textPrimary} />
          <Text style={styles.sortButtonText}>
            Sort: {localFilter.sortBy ? 
              localFilter.sortBy.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 
              'Popular'
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderProductItem = ({ item, index }: { item: any; index: number }) => (
    <View style={[
      styles.productItem,
      viewMode === 'list' && styles.productItemList,
      index % 2 === 0 && viewMode === 'grid' && styles.productItemLeft
    ]}>
      <ProductCard
        product={item}
        onPress={() => handleProductPress(item)}
        onAddToCart={() => handleAddToCart(item)}
        showAddToCart={true}
        style={viewMode === 'list' ? styles.productCardList : styles.productCardGrid}
      />
    </View>
  );

  const renderFooter = () => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={ELuxeColors.primary} />
        <Text style={styles.loadingText}>Loading more products...</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id || item._id}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // Force re-render when view mode changes
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        onEndReachedThreshold={0.1}
        onEndReached={loadMoreProducts}
        refreshing={isLoading}
        onRefresh={() => loadProducts(1, false)}
        ListFooterComponent={renderFooter}
      />

      {renderFilterModal()}
      {renderSortModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ComponentColors.screen.background,
  },
  header: {
    backgroundColor: ELuxeColors.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultCount: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  viewModeButton: {
    padding: 8,
  },
  headerBottom: {
    flexDirection: 'row',
    gap: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  filterButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.grey2,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
    flex: 1,
  },
  sortButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.textPrimary,
  },
  listContainer: {
    padding: 16,
  },
  productItem: {
    flex: 1,
    marginBottom: 16,
  },
  productItemList: {
    flex: 1,
  },
  productItemLeft: {
    marginRight: 8,
  },
  productCardGrid: {
    // Style pour vue grille
  },
  productCardList: {
    // Style pour vue liste
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: ELuxeColors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
  },
  modalTitle: {
    ...TextStyles.h3,
    color: ELuxeColors.textPrimary,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  filterSection: {
    marginVertical: 20,
  },
  filterTitle: {
    ...TextStyles.h4,
    color: ELuxeColors.textPrimary,
    marginBottom: 12,
  },
  filterOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: ELuxeColors.grey2,
  },
  filterOptionActive: {
    backgroundColor: ELuxeColors.primary,
  },
  filterOptionText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
  },
  filterOptionTextActive: {
    color: ELuxeColors.white,
  },
  sortOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border2,
  },
  sortOptionActive: {
    backgroundColor: ELuxeColors.primaryLight,
  },
  sortOptionText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
  },
  sortOptionTextActive: {
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
  modalFooter: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: ELuxeColors.border1,
    gap: 12,
  },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ELuxeColors.border1,
    alignItems: 'center',
  },
  clearButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.textPrimary,
  },
  applyButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: ELuxeColors.primary,
    alignItems: 'center',
  },
  applyButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
  },
  // Loading footer styles
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
  },
});

export default ShopScreen;
