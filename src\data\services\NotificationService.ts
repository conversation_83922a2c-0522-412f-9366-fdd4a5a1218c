import { Platform, Alert, Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  type: 'order' | 'promotion' | 'general' | 'reminder';
  data?: Record<string, any>;
  timestamp: Date;
  isRead: boolean;
}

export interface NotificationSettings {
  orderUpdates: boolean;
  promotions: boolean;
  newArrivals: boolean;
  priceDrops: boolean;
  generalNotifications: boolean;
  sound: boolean;
  vibration: boolean;
}

class NotificationService {
  private fcmToken: string | null = null;
  private isInitialized = false;
  private notifications: NotificationData[] = [];

  // Initialize notification service
  async initialize(): Promise<boolean> {
    try {
      // TODO: Initialize Firebase Cloud Messaging
      // import messaging from '@react-native-firebase/messaging';
      
      console.log('Initializing notification service...');
      
      // Request permission
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        console.log('Notification permission denied');
        return false;
      }

      // Get FCM token
      await this.getFCMToken();
      
      // Set up message handlers
      this.setupMessageHandlers();
      
      // Load cached notifications
      await this.loadCachedNotifications();
      
      this.isInitialized = true;
      console.log('Notification service initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
      return false;
    }
  }

  // Request notification permission
  async requestPermission(): Promise<boolean> {
    try {
      // TODO: Replace with real permission request
      // import messaging from '@react-native-firebase/messaging';
      // const authStatus = await messaging().requestPermission();
      // const enabled = authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      //                 authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      
      // For now, simulate permission granted
      const enabled = true;
      
      if (!enabled) {
        Alert.alert(
          'Notifications Disabled',
          'Please enable notifications in your device settings to receive important updates about your orders.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Linking.openSettings() },
          ]
        );
      }
      
      return enabled;
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  }

  // Get FCM token
  async getFCMToken(): Promise<string | null> {
    try {
      // TODO: Replace with real FCM token
      // import messaging from '@react-native-firebase/messaging';
      // const token = await messaging().getToken();
      
      // Simulate token
      const token = `fcm_token_${Date.now()}`;
      this.fcmToken = token;
      
      // Save token to backend
      await this.saveTokenToBackend(token);
      
      console.log('FCM Token:', token);
      return token;
    } catch (error) {
      console.error('Failed to get FCM token:', error);
      return null;
    }
  }

  // Save token to backend
  private async saveTokenToBackend(token: string): Promise<void> {
    try {
      // TODO: Send token to your backend
      console.log('Saving FCM token to backend:', token);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to save token to backend:', error);
    }
  }

  // Setup message handlers
  private setupMessageHandlers(): void {
    // TODO: Setup real message handlers
    // import messaging from '@react-native-firebase/messaging';
    
    // Handle background messages
    // messaging().setBackgroundMessageHandler(async remoteMessage => {
    //   console.log('Message handled in the background!', remoteMessage);
    //   await this.handleBackgroundMessage(remoteMessage);
    // });
    
    // Handle foreground messages
    // messaging().onMessage(async remoteMessage => {
    //   console.log('Message handled in the foreground!', remoteMessage);
    //   await this.handleForegroundMessage(remoteMessage);
    // });
    
    // Handle notification opened app
    // messaging().onNotificationOpenedApp(remoteMessage => {
    //   console.log('Notification caused app to open from background state:', remoteMessage);
    //   this.handleNotificationPress(remoteMessage);
    // });
    
    // Check whether an initial notification is available
    // messaging().getInitialNotification().then(remoteMessage => {
    //   if (remoteMessage) {
    //     console.log('Notification caused app to open from quit state:', remoteMessage);
    //     this.handleNotificationPress(remoteMessage);
    //   }
    // });
    
    console.log('Message handlers setup complete');
  }

  // Handle background message
  private async handleBackgroundMessage(remoteMessage: any): Promise<void> {
    try {
      const notification = this.parseRemoteMessage(remoteMessage);
      await this.saveNotification(notification);
    } catch (error) {
      console.error('Error handling background message:', error);
    }
  }

  // Handle foreground message
  private async handleForegroundMessage(remoteMessage: any): Promise<void> {
    try {
      const notification = this.parseRemoteMessage(remoteMessage);
      await this.saveNotification(notification);
      
      // Show in-app notification
      Toast.show({
        type: 'info',
        text1: notification.title,
        text2: notification.body,
        onPress: () => this.handleNotificationPress(remoteMessage),
      });
    } catch (error) {
      console.error('Error handling foreground message:', error);
    }
  }

  // Handle notification press
  private handleNotificationPress(remoteMessage: any): void {
    try {
      const data = remoteMessage.data;
      
      // Navigate based on notification type
      switch (data?.type) {
        case 'order':
          // Navigate to order details
          console.log('Navigate to order:', data.orderId);
          break;
        case 'promotion':
          // Navigate to promotion
          console.log('Navigate to promotion:', data.promotionId);
          break;
        default:
          // Navigate to notifications list
          console.log('Navigate to notifications');
          break;
      }
    } catch (error) {
      console.error('Error handling notification press:', error);
    }
  }

  // Parse remote message
  private parseRemoteMessage(remoteMessage: any): NotificationData {
    return {
      id: remoteMessage.messageId || `notif_${Date.now()}`,
      title: remoteMessage.notification?.title || 'E-Luxe',
      body: remoteMessage.notification?.body || 'You have a new notification',
      type: remoteMessage.data?.type || 'general',
      data: remoteMessage.data,
      timestamp: new Date(),
      isRead: false,
    };
  }

  // Save notification
  private async saveNotification(notification: NotificationData): Promise<void> {
    try {
      this.notifications.unshift(notification);
      
      // Keep only last 100 notifications
      if (this.notifications.length > 100) {
        this.notifications = this.notifications.slice(0, 100);
      }
      
      // Cache notifications
      await AsyncStorage.setItem(
        'notifications',
        JSON.stringify(this.notifications)
      );
    } catch (error) {
      console.error('Failed to save notification:', error);
    }
  }

  // Load cached notifications
  private async loadCachedNotifications(): Promise<void> {
    try {
      const cached = await AsyncStorage.getItem('notifications');
      if (cached) {
        this.notifications = JSON.parse(cached).map((notif: any) => ({
          ...notif,
          timestamp: new Date(notif.timestamp),
        }));
      }
    } catch (error) {
      console.error('Failed to load cached notifications:', error);
    }
  }

  // Get all notifications
  getNotifications(): NotificationData[] {
    return this.notifications;
  }

  // Get unread notifications count
  getUnreadCount(): number {
    return this.notifications.filter(notif => !notif.isRead).length;
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notification = this.notifications.find(notif => notif.id === notificationId);
      if (notification) {
        notification.isRead = true;
        await AsyncStorage.setItem(
          'notifications',
          JSON.stringify(this.notifications)
        );
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<void> {
    try {
      this.notifications.forEach(notif => {
        notif.isRead = true;
      });
      
      await AsyncStorage.setItem(
        'notifications',
        JSON.stringify(this.notifications)
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }

  // Clear all notifications
  async clearAllNotifications(): Promise<void> {
    try {
      this.notifications = [];
      await AsyncStorage.removeItem('notifications');
    } catch (error) {
      console.error('Failed to clear notifications:', error);
    }
  }

  // Get notification settings
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const settings = await AsyncStorage.getItem('notification_settings');
      if (settings) {
        return JSON.parse(settings);
      }
      
      // Default settings
      return {
        orderUpdates: true,
        promotions: true,
        newArrivals: true,
        priceDrops: true,
        generalNotifications: true,
        sound: true,
        vibration: true,
      };
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return {
        orderUpdates: true,
        promotions: true,
        newArrivals: true,
        priceDrops: true,
        generalNotifications: true,
        sound: true,
        vibration: true,
      };
    }
  }

  // Update notification settings
  async updateNotificationSettings(settings: NotificationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'notification_settings',
        JSON.stringify(settings)
      );
      
      // Update backend preferences
      await this.updateBackendPreferences(settings);
      
      Toast.show({
        type: 'success',
        text1: 'Settings Updated',
        text2: 'Your notification preferences have been saved',
      });
    } catch (error) {
      console.error('Failed to update notification settings:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to update notification settings',
      });
    }
  }

  // Update backend preferences
  private async updateBackendPreferences(settings: NotificationSettings): Promise<void> {
    try {
      // TODO: Send preferences to backend
      console.log('Updating backend notification preferences:', settings);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to update backend preferences:', error);
    }
  }

  // SUPPRIMÉ: Méthode de test - utilisation exclusive des notifications API

  // Check if service is initialized
  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  // Get FCM token
  getToken(): string | null {
    return this.fcmToken;
  }
}

export default new NotificationService();
