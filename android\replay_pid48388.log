JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 214 ciObject found
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/gradle/api/tasks/testing/TestDescriptor
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestListenerBuildOperationAdapter
instanceKlass org/gradle/api/internal/tasks/testing/results/TestListenerInternal
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestExecutionBuildOperationBuildSessionScopeServices
instanceKlass org/gradle/api/internal/catalog/DependenciesAccessorsWorkspaceProvider
instanceKlass org/gradle/internal/execution/workspace/WorkspaceProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildSessionScopeServices
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactSetToFileCollectionFactory
instanceKlass org/gradle/workers/internal/WorkerExecutionQueueFactory
instanceKlass org/gradle/process/internal/worker/child/WorkerDirectoryProvider
instanceKlass org/gradle/internal/work/ConditionalExecutionQueueFactory
instanceKlass org/gradle/workers/internal/WorkersServices$BuildSessionScopeServices
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations
instanceKlass org/gradle/internal/execution/OutputSnapshotter
instanceKlass org/gradle/internal/execution/InputFingerprinter
instanceKlass org/gradle/internal/execution/FileCollectionFingerprinterRegistry
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$BuildSessionServices
instanceKlass org/gradle/internal/build/BuildLayoutValidator
instanceKlass org/gradle/internal/buildtree/BuildTreeModelControllerServices
instanceKlass org/gradle/internal/session/BuildSessionActionExecutor
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingBuildSessionScopeServices
instanceKlass org/gradle/internal/snapshot/impl/ValueSnapshotterSerializerRegistry
instanceKlass org/gradle/internal/session/BuildSessionScopeServices$CrossBuildFileHashCacheWrapper
instanceKlass org/gradle/internal/buildevents/BuildStartedTime
instanceKlass org/gradle/internal/scopeids/id/ScopeId
instanceKlass org/gradle/internal/scopeids/PersistentScopeIdLoader
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributesFactory
instanceKlass org/gradle/api/internal/attributes/ImmutableAttributesFactory
instanceKlass org/gradle/initialization/layout/ProjectCacheDir
instanceKlass org/gradle/deployment/internal/DefaultDeploymentRegistry
instanceKlass org/gradle/deployment/internal/PendingChangesListener
instanceKlass org/gradle/deployment/internal/DeploymentRegistryInternal
instanceKlass org/gradle/deployment/internal/DeploymentRegistry
instanceKlass org/gradle/deployment/internal/PendingChangesManager
instanceKlass org/gradle/initialization/SettingsLocation
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputReader
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputHandler
instanceKlass org/gradle/internal/hash/ChecksumService
instanceKlass org/gradle/cache/scopes/BuildTreeScopedCache
instanceKlass org/gradle/api/internal/project/CrossProjectConfigurator
instanceKlass org/gradle/internal/snapshot/ValueSnapshotter
instanceKlass org/gradle/internal/service/scopes/WorkerSharedBuildSessionScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$Services
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/UserHomeScopedCompileCaches
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/GeneralCompileCaches
instanceKlass org/gradle/api/internal/tasks/CompileServices$UserHomeScopeServices
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/provider/plugins/precompiled/DefaultPrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/PrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultKotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/plugins/GradleUserHomeServices
instanceKlass org/gradle/api/internal/artifacts/transform/ImmutableTransformationWorkspaceServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationWorkspaceServices
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryStore
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryCacheAccess
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCachesProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$WritableArtifactCacheLockingParameters
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices
instanceKlass org/gradle/workers/internal/ClassLoaderStructureProvider
instanceKlass org/gradle/workers/internal/WorkerDaemonClientsManager
instanceKlass org/gradle/workers/internal/ActionExecutionSpecFactory
instanceKlass org/gradle/workers/internal/WorkersServices$GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptClassloadingCache
instanceKlass org/gradle/kotlin/dsl/provider/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/support/EmbeddedKotlinProvider
instanceKlass org/gradle/kotlin/dsl/support/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/cache/KotlinDslWorkspaceProvider
instanceKlass org/gradle/kotlin/dsl/cache/GradleUserHomeServices
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistryFactory
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy
instanceKlass org/gradle/internal/build/BuildState
instanceKlass org/gradle/internal/watch/vfs/impl/LocationsWrittenByCurrentBuild
instanceKlass org/gradle/internal/vfs/FileSystemAccess$WriteListener
instanceKlass org/gradle/api/internal/changedetection/state/CrossBuildFileHashCache
instanceKlass org/gradle/internal/hash/FileHasher
instanceKlass org/gradle/internal/watch/vfs/BuildLifecycleAwareVirtualFileSystem
instanceKlass org/gradle/internal/watch/vfs/FileSystemWatchingInformation
instanceKlass org/gradle/internal/vfs/VirtualFileSystem
instanceKlass org/gradle/internal/watch/vfs/WatchableFileSystemDetector
instanceKlass org/gradle/internal/fingerprint/classpath/ClasspathFingerprinter
instanceKlass org/gradle/internal/execution/FileCollectionFingerprinter
instanceKlass org/gradle/internal/watch/vfs/FileChangeListeners
instanceKlass org/gradle/internal/execution/FileCollectionSnapshotter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceSnapshotterCacheService
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderRegistry
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderFactory
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGradleUserHomeScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$InstanceUnpackingVisitor
instanceKlass org/gradle/internal/classpath/ClasspathFileTransformer
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer$Transform
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer
instanceKlass org/gradle/api/internal/changedetection/state/FileTimeStampInspector
instanceKlass org/gradle/initialization/RootBuildLifecycleListener
instanceKlass org/gradle/cache/internal/FilesFinder
instanceKlass org/gradle/cache/CacheCleanupStrategy
instanceKlass org/gradle/cache/CleanupAction
instanceKlass org/gradle/internal/file/FileAccessTracker
instanceKlass org/gradle/internal/classpath/DefaultClasspathTransformerCacheFactory
instanceKlass org/gradle/internal/classpath/ClasspathTransformerCacheFactory
instanceKlass org/gradle/internal/classpath/ClasspathBuilder$EntryBuilder
instanceKlass org/gradle/internal/classpath/ClasspathBuilder
instanceKlass org/gradle/internal/classpath/ClasspathEntryVisitor$Entry
instanceKlass org/gradle/internal/classpath/ClasspathWalker
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupServices$1
instanceKlass org/gradle/cache/CleanupProgressMonitor
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupService
instanceKlass org/gradle/cache/internal/VersionSpecificCacheDirectoryScanner
instanceKlass org/gradle/cache/internal/UsedGradleVersionsFromGradleUserHomeCaches
instanceKlass org/gradle/cache/CleanupFrequency$3
instanceKlass org/gradle/cache/CleanupFrequency$2
instanceKlass org/gradle/cache/CleanupFrequency$1
instanceKlass org/gradle/api/internal/cache/DefaultCleanup
instanceKlass org/gradle/api/internal/cache/CleanupInternal
instanceKlass org/gradle/api/internal/provider/TypeSanitizingTransformer
instanceKlass org/gradle/internal/time/TimestampSuppliers$1
instanceKlass org/gradle/internal/time/TimestampSuppliers
instanceKlass org/gradle/internal/serialization/Cached
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory$ManagedPropertyName
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Present
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Missing
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Value
instanceKlass org/gradle/api/internal/provider/Providers
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$4
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$3
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$2
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$1
instanceKlass org/gradle/api/internal/provider/ValueCollector
instanceKlass org/gradle/api/internal/provider/ValueSanitizer
instanceKlass org/gradle/api/internal/provider/ValueSanitizers
instanceKlass org/gradle/internal/Describables$AbstractDescribable
instanceKlass org/gradle/internal/Describables
instanceKlass org/gradle/api/internal/provider/AbstractProperty$FinalizationState
instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$DefaultCacheResourceConfiguration
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ObjectCreationDetails
instanceKlass org/gradle/internal/deprecation/DeprecationLogger$4
instanceKlass org/gradle/internal/deprecation/DeprecationLogger$ThrowingFactory
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/gradle/internal/instantiation/generator/InjectUtil
instanceKlass java/util/TimSort
instanceKlass com/google/common/collect/Iterables
instanceKlass com/google/common/collect/Ordering
instanceKlass org/gradle/internal/instantiation/generator/ConstructorComparator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$InvokeConstructorStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl$GeneratedConstructorImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$SerializationConstructor
instanceKlass org/objectweb/asm/Handler
instanceKlass org/objectweb/asm/Attribute
instanceKlass org/gradle/model/internal/asm/AsmClassGeneratorUtils
instanceKlass org/apache/groovy/util/BeanUtils
instanceKlass groovy/lang/MetaProperty
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSet$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection$WrappedIterator
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/gradle/api/Task
instanceKlass org/objectweb/asm/Edge
instanceKlass org/objectweb/asm/Label
instanceKlass org/objectweb/asm/Frame
instanceKlass org/gradle/model/internal/asm/BytecodeFragment
instanceKlass org/objectweb/asm/ByteVector
instanceKlass org/objectweb/asm/Symbol
instanceKlass org/objectweb/asm/SymbolTable
instanceKlass org/objectweb/asm/MethodVisitor
instanceKlass org/objectweb/asm/FieldVisitor
instanceKlass org/objectweb/asm/ModuleVisitor
instanceKlass org/objectweb/asm/AnnotationVisitor
instanceKlass org/objectweb/asm/RecordComponentVisitor
instanceKlass org/gradle/model/internal/asm/AsmClassGenerator
instanceKlass org/gradle/internal/DisplayName
instanceKlass org/gradle/api/internal/provider/AbstractMinimalProvider
instanceKlass org/gradle/api/internal/provider/PropertyInternal
instanceKlass org/gradle/api/internal/provider/HasConfigurableValueInternal
instanceKlass org/gradle/api/internal/provider/ProviderInternal
instanceKlass org/gradle/api/internal/provider/ValueSupplier
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory
instanceKlass org/gradle/util/internal/ConfigureUtil
instanceKlass org/gradle/internal/metaobject/AbstractDynamicObject
instanceKlass org/gradle/api/plugins/Convention
instanceKlass org/gradle/api/plugins/ExtensionContainer
instanceKlass org/gradle/internal/metaobject/DynamicObject
instanceKlass org/gradle/internal/metaobject/PropertyAccess
instanceKlass org/gradle/internal/metaobject/MethodAccess
instanceKlass org/gradle/api/plugins/ExtensionAware
instanceKlass org/gradle/internal/extensibility/ConventionAwareHelper
instanceKlass org/gradle/api/internal/HasConvention
instanceKlass org/gradle/api/internal/IConventionAware
instanceKlass org/gradle/internal/state/OwnerAware
instanceKlass org/gradle/api/internal/GeneratedSubclass
instanceKlass org/gradle/api/internal/ConventionMapping
instanceKlass groovy/lang/GroovyObjectSupport
instanceKlass groovy/lang/GroovyCallable
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass java/lang/Deprecated
instanceKlass org/gradle/api/internal/DynamicObjectAware
instanceKlass org/gradle/internal/extensibility/NoConventionMapping
instanceKlass org/gradle/api/Incubating
instanceKlass org/gradle/api/NonExtensible
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/gradle/internal/reflect/PropertyMutator
instanceKlass org/gradle/internal/reflect/PropertyAccessor
instanceKlass org/gradle/internal/reflect/JavaPropertyReflectionUtil
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassMetadata
instanceKlass org/gradle/internal/reflect/MutablePropertyDetails
instanceKlass java/beans/Introspector$1
instanceKlass jdk/internal/access/JavaBeansAccess
instanceKlass java/beans/FeatureDescriptor
instanceKlass java/beans/Introspector
instanceKlass org/gradle/internal/reflect/MethodSet$MethodKey
instanceKlass org/gradle/api/cache/Cleanup
instanceKlass org/gradle/api/internal/cache/CacheResourceConfigurationInternal
instanceKlass org/gradle/cache/CleanupFrequency
instanceKlass org/gradle/api/cache/CacheResourceConfiguration
instanceKlass org/gradle/internal/reflect/PropertyDetails
instanceKlass org/gradle/internal/reflect/MutableClassDetails
instanceKlass org/gradle/internal/reflect/ClassDetails
instanceKlass org/gradle/internal/reflect/ClassInspector
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationVisitor
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassInspectionVisitorImpl
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InjectionAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$DisabledAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassValidator
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/gradle/internal/reflect/MethodSet
instanceKlass com/google/common/collect/SetMultimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationHandler
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$CachedConstructor
instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations
instanceKlass org/gradle/api/internal/model/DefaultObjectFactory
instanceKlass org/gradle/internal/state/Managed
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass org/gradle/model/internal/inspect/ValidationProblemCollector
instanceKlass org/gradle/api/internal/MutationGuards$1
instanceKlass org/gradle/api/internal/MutationGuard
instanceKlass org/gradle/api/internal/MutationGuards
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator$1
instanceKlass org/gradle/api/internal/collections/DefaultDomainObjectCollectionFactory
instanceKlass org/gradle/api/file/Directory
instanceKlass org/gradle/api/file/RegularFile
instanceKlass org/gradle/api/file/FileSystemLocation
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependencyFactory
instanceKlass org/gradle/api/internal/file/collections/MinimalFileTree
instanceKlass org/gradle/api/internal/file/collections/MinimalFileCollection
instanceKlass org/gradle/api/internal/file/FileTreeInternal
instanceKlass org/gradle/api/internal/file/FileCollectionInternal
instanceKlass org/gradle/api/internal/tasks/TaskDependencyContainer
instanceKlass org/gradle/api/internal/file/DefaultFileCollectionFactory
instanceKlass org/gradle/internal/exceptions/DiagnosticsVisitor
instanceKlass org/gradle/internal/typeconversion/ErrorHandlingNotationParser
instanceKlass org/gradle/internal/typeconversion/NotationConvertResult
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter
instanceKlass org/gradle/internal/typeconversion/TypeInfo
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder
instanceKlass org/gradle/api/internal/file/FileOrUriNotationConverter
instanceKlass org/gradle/api/internal/file/AbstractFileResolver
instanceKlass org/gradle/api/internal/provider/DefaultPropertyFactory
instanceKlass org/gradle/internal/state/ModelObject
instanceKlass org/gradle/api/internal/file/collections/DefaultDirectoryFileTreeFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets$PatternSetFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/HeapProportionalCacheSizer
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme$DefaultDeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/InstanceFactory
instanceKlass org/gradle/internal/instantiation/generator/DependencyInjectingInstantiator
instanceKlass org/gradle/internal/instantiation/DeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme
instanceKlass javax/inject/Inject
instanceKlass org/gradle/internal/instantiation/generator/ParamsMatchingConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/common/collect/ImmutableMultimap$Builder
instanceKlass com/google/common/collect/Multiset
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedClass
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache
instanceKlass org/gradle/internal/session/BuildSessionLifecycleListener
instanceKlass org/gradle/model/internal/asm/ClassGeneratorSuffixRegistry
instanceKlass org/gradle/api/DomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectContainer
instanceKlass org/gradle/util/Configurable
instanceKlass org/gradle/api/NamedDomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectCollection
instanceKlass org/gradle/api/DomainObjectCollection
instanceKlass org/gradle/api/file/DirectoryProperty
instanceKlass org/gradle/api/file/RegularFileProperty
instanceKlass org/gradle/api/file/FileSystemLocationProperty
instanceKlass org/gradle/api/provider/Property
instanceKlass org/gradle/api/provider/MapProperty
instanceKlass org/gradle/api/provider/SetProperty
instanceKlass org/gradle/api/provider/ListProperty
instanceKlass org/gradle/api/provider/HasMultipleValues
instanceKlass org/gradle/api/provider/Provider
instanceKlass org/gradle/api/file/ConfigurableFileTree
instanceKlass org/gradle/api/file/DirectoryTree
instanceKlass org/gradle/api/file/FileTree
instanceKlass org/gradle/api/file/ConfigurableFileCollection
instanceKlass org/gradle/api/provider/HasConfigurableValue
instanceKlass org/gradle/api/file/FileCollection
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InstantiationStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassInspectionVisitor
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$UnclaimedPropertyHandler
instanceKlass com/google/common/reflect/TypeCapture
instanceKlass com/google/common/collect/ListMultimap
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass com/google/common/collect/Multimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator
instanceKlass org/gradle/api/internal/tasks/properties/annotations/OutputPropertyRoleAnnotationHandler
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory$ManagedTypeFactory
instanceKlass org/gradle/internal/instantiation/InstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectionService
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass org/gradle/work/NormalizeLineEndings
instanceKlass org/gradle/api/tasks/IgnoreEmptyDirectories
instanceKlass org/gradle/api/tasks/Optional
instanceKlass org/gradle/api/tasks/PathSensitive
instanceKlass org/gradle/api/tasks/CompileClasspath
instanceKlass org/gradle/api/tasks/Classpath
instanceKlass org/gradle/api/tasks/SkipWhenEmpty
instanceKlass org/gradle/work/Incremental
instanceKlass org/gradle/internal/fingerprint/FileNormalizer
instanceKlass org/gradle/internal/reflect/annotations/AnnotationCategory
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectingVisitor
instanceKlass org/gradle/internal/cache/MonitoredCleanupAction
instanceKlass java/util/function/LongSupplier
instanceKlass org/gradle/internal/file/impl/DefaultDeleter
instanceKlass org/gradle/cache/internal/scopes/DefaultCacheScopeMapping
instanceKlass org/gradle/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/DefaultCacheRepository
instanceKlass org/gradle/cache/internal/ReferencablePersistentCache
instanceKlass org/gradle/cache/PersistentCache
instanceKlass org/gradle/cache/HasCleanupAction
instanceKlass org/gradle/cache/CleanableStore
instanceKlass org/gradle/cache/CacheAccess
instanceKlass org/gradle/cache/internal/DefaultCacheFactory
instanceKlass org/gradle/internal/logging/services/ProgressLoggingBridge
instanceKlass org/gradle/internal/logging/progress/ProgressLogger
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory
instanceKlass org/gradle/internal/operations/DefaultBuildOperationIdFactory
instanceKlass org/gradle/cache/internal/UsedGradleVersions
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupServices
instanceKlass org/gradle/internal/classloader/ClasspathHasher
instanceKlass org/gradle/cache/internal/scopes/AbstractScopedCache
instanceKlass org/gradle/initialization/layout/GlobalCacheDir
instanceKlass org/gradle/api/internal/cache/CacheConfigurationsInternal
instanceKlass org/gradle/api/cache/CacheConfigurations
instanceKlass org/gradle/internal/vfs/FileSystemAccess
instanceKlass org/gradle/cache/internal/DefaultGeneratedGradleJarCache
instanceKlass org/gradle/cache/internal/GeneratedGradleJarCache
instanceKlass org/gradle/groovy/scripts/internal/CrossBuildInMemoryCachingScriptClassCache
instanceKlass org/gradle/cache/internal/GradleUserHomeCacheCleanupActionDecorator
instanceKlass org/gradle/internal/cache/MonitoredCleanupActionDecorator
instanceKlass org/gradle/cache/internal/CleanupActionDecorator
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListenerManager
instanceKlass org/gradle/cache/scopes/GlobalScopedCache
instanceKlass org/gradle/process/internal/worker/child/WorkerProcessClassPathProvider
instanceKlass org/gradle/internal/jvm/JavaModuleDetector
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1
instanceKlass org/gradle/internal/session/BuildSessionState
instanceKlass org/gradle/internal/operations/trace/SerializedOperation
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager
instanceKlass org/gradle/configuration/internal/DefaultDynamicCallContextTracker
instanceKlass org/gradle/configuration/internal/DynamicCallContextTracker
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLease
instanceKlass org/gradle/internal/resources/ResourceLock
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLeaseCompletion
instanceKlass com/google/common/base/Supplier
instanceKlass org/gradle/internal/work/Synchronizer
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService
instanceKlass org/gradle/internal/resources/ResourceLockState
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService
instanceKlass org/gradle/internal/resources/ResourceLockCoordinationService
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationValve
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListenerRegistrar
instanceKlass org/gradle/internal/operations/logging/LoggingBuildOperationProgressBroadcaster
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace
instanceKlass org/gradle/internal/work/WorkerLeaseService
instanceKlass org/gradle/internal/work/WorkerThreadRegistry
instanceKlass org/gradle/internal/resources/ProjectLeaseRegistry
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator
instanceKlass org/gradle/configuration/internal/ListenerBuildOperationDecorator
instanceKlass org/gradle/configuration/internal/UserCodeApplicationContext
instanceKlass org/gradle/internal/operations/BuildOperationExecutor
instanceKlass org/gradle/internal/operations/BuildOperationRunner
instanceKlass org/gradle/internal/operations/BuildOperationQueueFactory
instanceKlass org/gradle/internal/session/CrossBuildSessionState$Services
instanceKlass org/gradle/internal/service/ServiceRegistryBuilder
instanceKlass org/gradle/internal/session/CrossBuildSessionState
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter$ActionImpl
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$3
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator
instanceKlass org/gradle/internal/logging/console/BuildLogLevelFilterRenderer
instanceKlass org/gradle/launcher/daemon/server/exec/ExecuteBuild$1
instanceKlass org/gradle/initialization/DefaultBuildRequestMetaData
instanceKlass org/gradle/initialization/DefaultBuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection$1
instanceKlass org/gradle/internal/featurelifecycle/LoggingIncubatingFeatureHandler
instanceKlass org/gradle/util/internal/IncubationLogger
instanceKlass org/gradle/internal/featurelifecycle/FeatureUsage
instanceKlass org/gradle/internal/featurelifecycle/UsageLocationReporter
instanceKlass org/gradle/internal/featurelifecycle/LoggingDeprecatedFeatureHandler
instanceKlass org/gradle/internal/featurelifecycle/FeatureHandler
instanceKlass org/gradle/internal/deprecation/DeprecationMessageBuilder
instanceKlass org/gradle/internal/deprecation/DeprecationLogger
instanceKlass org/gradle/cache/internal/SimpleStateCache$1
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$2
instanceKlass org/gradle/util/internal/StdinSwapper$2
instanceKlass org/gradle/util/internal/StdinSwapper$1
instanceKlass org/gradle/util/internal/Swapper
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$1
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher$1
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass com/google/common/collect/AbstractIterator$1
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$1
instanceKlass org/gradle/launcher/daemon/server/CompatibleDaemonExpirationStrategy$1
instanceKlass java/math/MathContext
instanceKlass org/gradle/internal/util/NumberUtil
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionStats
instanceKlass java/util/concurrent/LinkedBlockingDeque$AbstractItr
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$5
instanceKlass org/gradle/launcher/daemon/server/exec/StartBuildOrRespondWithBusy$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue$1
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel$1
instanceKlass com/google/common/collect/Platform
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandExecution
instanceKlass org/gradle/launcher/exec/DefaultBuildActionParameters
instanceKlass org/gradle/configuration/GradleLauncherMetaData
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass org/gradle/internal/DefaultTaskExecutionRequest
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass org/gradle/internal/buildoption/Option$Value
instanceKlass org/gradle/internal/RunDefaultTasksExecutionRequest
instanceKlass org/gradle/TaskExecutionRequest
instanceKlass org/gradle/api/launcher/cli/WelcomeMessageConfiguration
instanceKlass org/gradle/internal/concurrent/DefaultParallelismConfiguration
instanceKlass org/gradle/internal/logging/DefaultLoggingConfiguration
instanceKlass org/gradle/initialization/BuildLayoutParameters
instanceKlass java/nio/channels/spi/AbstractSelector$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$ReceiveQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$DisconnectQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection
instanceKlass org/gradle/launcher/daemon/server/api/DaemonConnection
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler$ConnectionWorker
instanceKlass org/gradle/launcher/daemon/server/SynchronizedDispatchConnection
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$2
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress$Serializer
instanceKlass org/gradle/internal/io/BufferCaster
instanceKlass java/lang/invoke/ConstantBootstraps
instanceKlass java/nio/channels/SelectionKey
instanceKlass java/nio/BufferMismatch
instanceKlass com/sun/security/sasl/Provider$1
instanceKlass sun/security/pkcs11/SunPKCS11$Descriptor
instanceKlass javax/security/auth/callback/CallbackHandler
instanceKlass javax/security/auth/Subject
instanceKlass com/sun/security/sasl/gsskerb/JdkSASL$1
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$2
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$1
instanceKlass sun/security/jgss/SunProvider$1
instanceKlass sun/security/smartcardio/SunPCSC$1
instanceKlass java/security/spec/ECFieldF2m
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerEncoder
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/ec/SunEC$1
instanceKlass sun/security/mscapi/SunMSCAPI$2
instanceKlass sun/security/mscapi/SunMSCAPI$1
instanceKlass sun/security/jca/ProviderConfig$ProviderLoader
instanceKlass sun/security/jca/ProviderConfig$3
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/nio/ch/UnixDomainSocketsUtil
instanceKlass sun/nio/ch/UnixDomainSockets
instanceKlass sun/nio/ch/PipeImpl$Initializer$LoopbackConnector
instanceKlass sun/nio/ch/PipeImpl$Initializer
instanceKlass java/nio/channels/Pipe
instanceKlass sun/nio/ch/WEPoll
instanceKlass sun/nio/ch/Util$2
instanceKlass java/nio/channels/Selector
instanceKlass org/gradle/internal/remote/internal/KryoBackedMessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection
instanceKlass org/gradle/internal/serialize/ObjectWriter
instanceKlass org/gradle/internal/serialize/ObjectReader
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter
instanceKlass org/gradle/internal/serialize/StatefulSerializer
instanceKlass org/gradle/internal/serialize/Serializers
instanceKlass org/gradle/internal/remote/internal/RemoteConnection
instanceKlass org/gradle/internal/remote/internal/Connection
instanceKlass org/gradle/internal/dispatch/Receive
instanceKlass org/gradle/internal/remote/internal/MessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnectCompletion
instanceKlass org/gradle/internal/remote/internal/ConnectCompletion
instanceKlass java/net/Socket
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$DefaultCommonPoolForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$1
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$DefaultDaemonExpirationListener
instanceKlass org/gradle/launcher/daemon/server/Daemon$DaemonExpirationPeriodicCheck
instanceKlass org/gradle/launcher/daemon/server/expiry/AnyDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUnavailableExpirationStrategy
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass org/gradle/internal/event/DefaultListenerManager$ListenerDetails
instanceKlass org/gradle/launcher/daemon/server/health/LowMemoryDaemonExpirationStrategy
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusListener
instanceKlass org/gradle/launcher/daemon/server/NotMostRecentlyUsedDaemonExpirationStrategy
instanceKlass com/google/common/base/Functions$ConstantFunction
instanceKlass com/google/common/base/Functions
instanceKlass org/gradle/launcher/daemon/server/DaemonIdleTimeoutExpirationStrategy
instanceKlass org/gradle/launcher/daemon/context/DaemonCompatibilitySpec
instanceKlass org/gradle/api/internal/specs/ExplainingSpec
instanceKlass org/gradle/launcher/daemon/server/CompatibleDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AllDaemonExpirationStrategy
instanceKlass org/gradle/internal/stream/EncodedStream
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonStartupCommunication
instanceKlass java/io/FileOutputStream$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress
instanceKlass org/gradle/internal/serialize/AbstractEncoder
instanceKlass org/gradle/internal/serialize/FlushableEncoder
instanceKlass org/gradle/launcher/daemon/registry/DaemonStopEvent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonStopEvent
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo$Serializer
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer$SequenceNumberLockState
instanceKlass sun/nio/ch/IOStatus
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass java/net/DatagramPacket
instanceKlass org/gradle/cache/internal/locklistener/FileLockPacketPayload
instanceKlass org/gradle/internal/io/IOQuery$Result
instanceKlass org/gradle/cache/internal/filelock/LockInfo
instanceKlass org/gradle/cache/internal/filelock/FileLockOutcome
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$1
instanceKlass org/gradle/internal/io/ExponentialBackoff
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$AwaitableFileLockReleasedSignal
instanceKlass org/gradle/cache/FileLockReleasedSignal
instanceKlass org/gradle/cache/internal/filelock/LockInfoSerializer
instanceKlass org/gradle/cache/internal/filelock/LockInfoAccess
instanceKlass org/gradle/cache/internal/filelock/LockStateAccess
instanceKlass org/gradle/cache/internal/filelock/LockFileAccess
instanceKlass org/gradle/cache/internal/filelock/LockState
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer
instanceKlass org/gradle/internal/io/IOQuery
instanceKlass org/gradle/cache/FileLock$State
instanceKlass org/gradle/cache/internal/filelock/LockStateSerializer
instanceKlass sun/nio/ch/ExtendedSocketOption$1
instanceKlass sun/nio/ch/ExtendedSocketOption
instanceKlass sun/nio/ch/OptionKey
instanceKlass sun/nio/ch/SocketOptionRegistry$LazyInitialization
instanceKlass sun/nio/ch/SocketOptionRegistry$RegistryKey
instanceKlass sun/nio/ch/SocketOptionRegistry
instanceKlass sun/nio/ch/DatagramChannelImpl$DefaultOptionsHolder
instanceKlass java/net/StandardSocketOptions$StdSocketOption
instanceKlass java/net/StandardSocketOptions
instanceKlass sun/nio/ch/DatagramSocketAdaptor$DatagramSockets
instanceKlass sun/nio/ch/NativeSocketAddress
instanceKlass sun/net/ResourceManager
instanceKlass java/nio/channels/MulticastChannel
instanceKlass java/net/DatagramSocket
instanceKlass org/gradle/cache/internal/locklistener/FileLockCommunicator
instanceKlass org/gradle/cache/internal/filelock/LockOptionsBuilder
instanceKlass org/gradle/cache/internal/SimpleStateCache$1Updater
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$8
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo
instanceKlass org/gradle/launcher/daemon/context/DaemonConnectDetails
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass jdk/internal/misc/ScopedMemoryAccess$Scope
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass java/time/ZoneId
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$1
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$Receiver
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddress
instanceKlass org/gradle/internal/remote/internal/inet/InetEndpoint
instanceKlass java/util/UUID$Holder
instanceKlass java/util/UUID
instanceKlass sun/net/NetHooks
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
instanceKlass java/net/Inet4AddressImpl
instanceKlass org/gradle/internal/remote/internal/inet/InetAddresses
instanceKlass sun/net/NetProperties$1
instanceKlass sun/net/NetProperties
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass java/net/ServerSocket
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions$1
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ext/ExtendedSocketOptions
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/DefaultSelectorProvider
instanceKlass java/nio/channels/spi/SelectorProvider$Holder
instanceKlass java/nio/channels/spi/SelectorProvider
instanceKlass java/nio/channels/NetworkChannel
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector$1
instanceKlass org/gradle/launcher/daemon/server/Daemon$5
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler
instanceKlass org/gradle/initialization/DefaultBuildCancellationToken
instanceKlass java/util/concurrent/SynchronousQueue$TransferStack$SNode
instanceKlass java/util/concurrent/SynchronousQueue$Transferer
instanceKlass org/gradle/initialization/BuildCancellationToken
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator
instanceKlass org/gradle/launcher/daemon/server/Daemon$4
instanceKlass org/gradle/launcher/daemon/server/Daemon$3
instanceKlass org/gradle/launcher/daemon/server/Daemon$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$1
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUpdater
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformNameService
instanceKlass java/net/InetAddress$NameService
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddressImplFactory
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InetAddress
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass java/lang/constant/DynamicConstantDesc
instanceKlass java/lang/constant/DirectMethodHandleDesc$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl
instanceKlass java/lang/constant/DirectMethodHandleDesc
instanceKlass java/lang/constant/MethodHandleDesc$1
instanceKlass java/lang/constant/MethodHandleDesc
instanceKlass java/lang/constant/MethodTypeDescImpl
instanceKlass java/lang/constant/MethodTypeDesc
instanceKlass java/lang/constant/ReferenceClassDescImpl
instanceKlass java/lang/constant/ConstantUtils
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/lang/constant/ConstantDescs
instanceKlass java/lang/invoke/VarHandle$2
instanceKlass java/lang/invoke/VarHandle$TypesAndInvokers
instanceKlass java/lang/invoke/VarHandleByteArrayBase
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/util/concurrent/LinkedBlockingDeque$Node
instanceKlass java/lang/management/MemoryUsage
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionEvent
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass com/google/common/base/Joiner
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonCommandExecuter
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddressSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent
instanceKlass org/gradle/cache/LockOptions
instanceKlass org/gradle/cache/internal/AbstractFileAccess
instanceKlass org/gradle/internal/serialize/Encoder
instanceKlass org/gradle/cache/internal/SimpleStateCache
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator
instanceKlass org/gradle/cache/PersistentStateCache$UpdateAction
instanceKlass org/gradle/cache/PersistentStateCache
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FallbackStat
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/EmptyChmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/jdk7/Jdk7Symlink
instanceKlass net/rubygrapefruit/platform/file/PosixFileInfo
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$BrokenService
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/UnavailablePosixFiles
instanceKlass net/rubygrapefruit/platform/terminal/Terminals
instanceKlass org/gradle/api/internal/file/temp/GradleUserHomeTemporaryFileProvider$1
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$2
instanceKlass net/rubygrapefruit/platform/file/WindowsFileInfo
instanceKlass net/rubygrapefruit/platform/file/FileInfo
instanceKlass net/rubygrapefruit/platform/internal/DirList
instanceKlass net/rubygrapefruit/platform/internal/AbstractFiles
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/NativePlatformBackedFileMetadataAccessor
instanceKlass org/gradle/internal/id/RandomLongIdGenerator
instanceKlass org/gradle/cache/internal/DefaultProcessMetaDataProvider
instanceKlass org/gradle/internal/io/ExponentialBackoff$Signal
instanceKlass org/gradle/cache/FileLock
instanceKlass org/gradle/cache/FileAccess
instanceKlass org/gradle/cache/internal/DefaultFileLockManager
instanceKlass org/gradle/internal/remote/ConnectionAcceptor
instanceKlass org/gradle/internal/remote/Address
instanceKlass java/net/SocketAddress
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$OutputMessageSerializer
instanceKlass org/gradle/internal/logging/serializer/LogLevelChangeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressCompleteEventSerializer
instanceKlass org/gradle/internal/operations/BuildOperationMetadata
instanceKlass org/gradle/internal/logging/serializer/ProgressStartEventSerializer
instanceKlass org/gradle/internal/logging/serializer/SpanSerializer
instanceKlass org/gradle/internal/logging/serializer/StyledTextOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputResumeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/PromptOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputRequestEventSerializer
instanceKlass org/gradle/internal/logging/serializer/LogEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CloseInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$ForwardInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FinishedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$SuccessSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FailureSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildStartedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$DaemonUnavailableSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CancelSerializer
instanceKlass org/gradle/launcher/exec/BuildActionParameters
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildActionParametersSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector
instanceKlass org/gradle/launcher/daemon/server/IncomingConnectionHandler
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStateControl
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$TypeInfo
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Iter
instanceKlass org/gradle/tooling/internal/protocol/test/InternalTaskSpec
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$InternalTaskSpecSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$TestExecutionRequestActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedPhasedActionSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializedPayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildEventSubscriptionsSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildModelActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/SubscribableBuildAction
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$InstanceBasedSerializerFactory
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ValueSerializer
instanceKlass org/gradle/internal/serialize/AbstractSerializer
instanceKlass org/gradle/internal/serialize/BaseSerializerFactory
instanceKlass org/gradle/internal/serialize/AbstractCollectionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$NullableFileSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ExecuteBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/ExecuteBuildAction
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$HierarchySerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$StrictSerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerClassMatcherStrategy
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$1
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerFactory
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry
instanceKlass org/gradle/internal/serialize/SerializerRegistry
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer
instanceKlass org/gradle/initialization/BuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection
instanceKlass org/gradle/launcher/daemon/server/exec/ResetDeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/RequestStopIfSingleUsedDaemon
instanceKlass org/gradle/launcher/daemon/server/api/StdinHandler
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput
instanceKlass org/gradle/launcher/daemon/server/exec/LogAndCheckHealth
instanceKlass org/gradle/launcher/daemon/server/exec/BuildCommandOnly
instanceKlass org/gradle/launcher/daemon/server/exec/ReturnResult
instanceKlass org/gradle/launcher/daemon/server/api/HandleReportStatus
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel
instanceKlass org/gradle/launcher/daemon/server/api/HandleInvalidateVirtualFileSystem
instanceKlass org/gradle/launcher/daemon/protocol/Message
instanceKlass org/gradle/launcher/daemon/server/api/HandleStop
instanceKlass org/gradle/launcher/daemon/diagnostics/DaemonDiagnostics
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter
instanceKlass org/gradle/tooling/internal/provider/StartParamsValidatingActionExecuter
instanceKlass org/gradle/initialization/BuildRequestMetaData
instanceKlass org/gradle/initialization/exception/ExceptionAnalyser
instanceKlass org/gradle/initialization/exception/ExceptionCollector
instanceKlass org/gradle/tooling/internal/provider/SessionFailureReportingActionExecuter
instanceKlass org/gradle/StartParameter
instanceKlass org/gradle/concurrent/ParallelismConfiguration
instanceKlass org/gradle/tooling/internal/provider/SetupLoggingActionExecuter
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderCache
instanceKlass org/gradle/internal/classloader/HashingClassLoaderFactory
instanceKlass org/gradle/internal/hash/ClassLoaderHierarchyHasher
instanceKlass org/gradle/groovy/scripts/internal/ScriptSourceHasher
instanceKlass org/gradle/internal/execution/timeout/TimeoutHandler
instanceKlass org/gradle/cache/internal/FileContentCacheFactory
instanceKlass org/gradle/cache/scopes/ScopedCache
instanceKlass org/gradle/cache/GlobalCacheLocations
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistry
instanceKlass org/gradle/internal/file/FileAccessTimeJournal
instanceKlass org/gradle/process/internal/worker/WorkerProcessFactory
instanceKlass org/gradle/cache/CacheRepository
instanceKlass org/gradle/cache/internal/CacheScopeMapping
instanceKlass org/gradle/internal/isolation/IsolatableFactory
instanceKlass org/gradle/internal/service/scopes/WorkerSharedUserHomeScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutputFactory
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationResult
instanceKlass org/gradle/internal/event/DefaultListenerManager$EventBroadcast
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationListener
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/FunctionalInterface
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$RegistrationWrapper
instanceKlass org/gradle/internal/service/scopes/Scopes$Build
instanceKlass org/gradle/internal/service/scopes/EventScope
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/util/concurrent/TimeUnit$1
instanceKlass kotlin/annotation/Target
instanceKlass kotlin/annotation/Retention
instanceKlass kotlin/Metadata
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildTree
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildSession
instanceKlass org/gradle/internal/service/scopes/Scopes$UserHome
instanceKlass org/gradle/internal/service/scopes/ServiceScope
instanceKlass javax/annotation/Nonnull
instanceKlass javax/annotation/meta/TypeQualifierDefault
instanceKlass java/lang/annotation/Target
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass com/google/common/collect/ElementTypesAreNonnullByDefault
instanceKlass java/lang/Class$AnnotationData
instanceKlass org/gradle/internal/service/scopes/StatefulListener
instanceKlass org/gradle/internal/service/scopes/Scope$Global
instanceKlass org/gradle/internal/service/scopes/Scope
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass org/gradle/internal/concurrent/ManagedExecutorImpl$1
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionCheck
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor$1
instanceKlass java/util/concurrent/BlockingDeque
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultSlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/SlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionInfo
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy$CatchAndRecordFailures
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/gradle/internal/concurrent/ThreadFactoryImpl
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedScheduledExecutor
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedExecutor
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass org/gradle/internal/concurrent/AsyncStoppable
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy
instanceKlass org/gradle/internal/concurrent/DefaultExecutorFactory
instanceKlass sun/management/Sensor
instanceKlass sun/management/MemoryPoolImpl
instanceKlass java/lang/management/MemoryPoolMXBean
instanceKlass org/gradle/util/internal/CollectionUtils
instanceKlass com/sun/jmx/mbeanserver/Util
instanceKlass javax/management/ObjectName$Property
instanceKlass com/sun/jmx/mbeanserver/GetPropertyAction
instanceKlass javax/management/ObjectName
instanceKlass javax/management/QueryExp
instanceKlass java/lang/invoke/LambdaFormEditor$1
instanceKlass java/lang/invoke/MethodHandles$1
instanceKlass java/lang/Long$LongCache
instanceKlass sun/management/Util
instanceKlass com/sun/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryMXBean
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass java/util/concurrent/Callable
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/ReduceOps
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collector
instanceKlass java/util/stream/Collectors
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$1
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryManagerMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionMonitor
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass org/gradle/internal/time/DefaultTimer
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$StateContext
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/OptionalInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/stream/IntStream
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass java/text/DecimalFormatSymbols
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/Calendar
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/Format
instanceKlass org/gradle/internal/logging/sink/LogEventDispatcher
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$SeenFromEol
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$4
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$3
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$2
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$1
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$State
instanceKlass org/gradle/internal/logging/text/StreamBackedStandardOutputListener
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutput
instanceKlass org/gradle/internal/logging/console/StyledTextOutputBackedRenderer
instanceKlass org/slf4j/helpers/FormattingTuple
instanceKlass org/slf4j/helpers/MessageFormatter
instanceKlass net/rubygrapefruit/platform/internal/FunctionResult
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$PrintStreamDestination
instanceKlass java/util/logging/ErrorManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter$SnapshotImpl
instanceKlass org/gradle/internal/dispatch/MethodInvocation
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$SnapshotImpl
instanceKlass org/gradle/process/internal/shutdown/ShutdownHooks
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonMain$1
instanceKlass com/google/common/io/Files$2
instanceKlass com/google/common/io/LineProcessor
instanceKlass com/google/common/io/ByteSink
instanceKlass com/google/common/io/ByteSource
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/graph/SuccessorsFunction
instanceKlass com/google/common/io/Files
instanceKlass org/gradle/util/internal/GFileUtils
instanceKlass org/gradle/util/GradleVersion
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext$Serializer
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext
instanceKlass net/rubygrapefruit/platform/internal/jni/PosixProcessFunctions
instanceKlass org/gradle/internal/FileUtils$1
instanceKlass org/gradle/internal/FileUtils
instanceKlass com/google/common/collect/Lists
instanceKlass org/gradle/internal/nativeintegration/ReflectiveEnvironment
instanceKlass org/gradle/internal/nativeintegration/processenvironment/AbstractProcessEnvironment
instanceKlass net/rubygrapefruit/platform/internal/DefaultProcess
instanceKlass net/rubygrapefruit/platform/internal/WrapperProcess
instanceKlass net/rubygrapefruit/platform/file/WindowsFiles
instanceKlass org/gradle/launcher/daemon/context/DaemonContextBuilder
instanceKlass org/gradle/internal/id/UUIDGenerator
instanceKlass org/gradle/internal/remote/internal/OutgoingConnector
instanceKlass org/gradle/internal/remote/MessagingServer
instanceKlass org/gradle/internal/remote/internal/IncomingConnector
instanceKlass org/gradle/internal/remote/MessagingClient
instanceKlass org/gradle/internal/id/IdGenerator
instanceKlass org/gradle/internal/remote/services/MessagingServices
instanceKlass org/gradle/api/internal/file/DefaultFileLookup
instanceKlass org/gradle/internal/scripts/DefaultScriptFileResolver
instanceKlass org/gradle/internal/scripts/ScriptFileResolver
instanceKlass org/gradle/initialization/layout/BuildLayoutFactory
instanceKlass org/gradle/tooling/internal/provider/runner/OperationDependencyLookup
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory
instanceKlass org/gradle/configurationcache/serialization/beans/BeanConstructors
instanceKlass org/gradle/nativeplatform/NativeBinarySpec
instanceKlass org/gradle/platform/base/BinarySpec
instanceKlass org/gradle/platform/base/Binary
instanceKlass org/gradle/api/CheckableComponentSpec
instanceKlass org/gradle/api/BuildableComponentSpec
instanceKlass org/gradle/platform/base/ComponentSpec
instanceKlass org/gradle/model/ModelElement
instanceKlass org/gradle/api/Buildable
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory
instanceKlass org/gradle/internal/resource/transport/sftp/SftpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/nativeplatform/TargetMachineBuilder
instanceKlass org/gradle/nativeplatform/TargetMachine
instanceKlass org/gradle/nativeplatform/internal/DefaultTargetMachineFactory
instanceKlass org/gradle/nativeplatform/TargetMachineFactory
instanceKlass org/gradle/nativeplatform/internal/NativePlatformResolver
instanceKlass org/gradle/platform/base/internal/PlatformResolver
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatformInternal
instanceKlass org/gradle/nativeplatform/platform/NativePlatform
instanceKlass org/gradle/platform/base/Platform
instanceKlass org/gradle/nativeplatform/platform/internal/OperatingSystemInternal
instanceKlass org/gradle/nativeplatform/platform/OperatingSystem
instanceKlass org/gradle/api/Named
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatforms
instanceKlass org/gradle/internal/logging/text/DiagnosticsVisitor
instanceKlass org/gradle/buildinit/plugins/internal/action/InitBuiltInCommand
instanceKlass org/gradle/api/component/SoftwareComponentFactory
instanceKlass org/gradle/api/plugins/internal/PluginAuthorServices$GlobalScopeServices
instanceKlass org/gradle/internal/build/event/BuildEventSubscriptions
instanceKlass org/gradle/internal/build/event/OperationResultPostProcessorFactory
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGlobalScopeServices
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/reporting/ReportRenderer
instanceKlass org/gradle/api/reporting/components/internal/DiagnosticsServices$1
instanceKlass org/gradle/api/plugins/internal/HelpBuiltInCommand
instanceKlass org/gradle/configuration/project/BuiltInCommand
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory
instanceKlass org/gradle/internal/resource/transport/http/SslContextFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/api/internal/artifacts/configurations/MarkConfigurationObservedListener
instanceKlass org/gradle/api/internal/artifacts/configurations/ProjectDependencyObservedListener
instanceKlass org/gradle/internal/resource/ExternalResourceName
instanceKlass org/gradle/api/Describable
instanceKlass org/gradle/api/artifacts/component/ComponentSelector
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport
instanceKlass org/gradle/api/internal/attributes/EmptySchema
instanceKlass org/gradle/api/internal/attributes/AttributesSchemaInternal
instanceKlass org/gradle/api/internal/attributes/DescribableAttributesSchema
instanceKlass org/gradle/api/attributes/AttributesSchema
instanceKlass org/gradle/cache/internal/ProducerGuard
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionParser
instanceKlass org/gradle/api/Transformer
instanceKlass org/gradle/internal/typeconversion/NotationParser
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformActionScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/AbstractIvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/IvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformParameterScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/ExcludeRuleConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DependencyDescriptorFactory
instanceKlass org/gradle/internal/resource/local/FileResourceRepository
instanceKlass org/gradle/internal/resource/ExternalResourceRepository
instanceKlass org/gradle/internal/typeconversion/NotationConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/IvyContextManager
instanceKlass org/gradle/api/internal/artifacts/ImmutableModuleIdentifierFactory
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/LocalComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalConfigurationMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGlobalScopeServices
instanceKlass org/gradle/kotlin/dsl/support/ImplicitImports
instanceKlass org/gradle/kotlin/dsl/support/GlobalServices
instanceKlass org/gradle/internal/build/event/BuildEventServices$1
instanceKlass org/gradle/internal/build/event/BuildEventListenerFactory
instanceKlass org/gradle/internal/operations/BuildOperationListener
instanceKlass org/gradle/initialization/BuildEventConsumer
instanceKlass org/gradle/internal/build/event/DefaultBuildEventsListenerRegistry
instanceKlass org/gradle/internal/build/event/BuildEventListenerRegistryInternal
instanceKlass org/gradle/build/event/BuildEventsListenerRegistry
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics$Collector
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics$Collector
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GlobalScopeServices
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistar
instanceKlass org/gradle/internal/operations/BuildOperationAncestryTracker
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistration
instanceKlass org/gradle/internal/properties/bean/PropertyWalker
instanceKlass org/gradle/api/internal/tasks/properties/TaskScheme
instanceKlass org/gradle/api/internal/tasks/properties/TypeScheme
instanceKlass org/gradle/api/model/ReplacedBy
instanceKlass org/gradle/api/tasks/Internal
instanceKlass org/gradle/api/services/ServiceReference
instanceKlass org/gradle/api/tasks/OutputFiles
instanceKlass org/gradle/api/tasks/OutputFile
instanceKlass org/gradle/api/tasks/OutputDirectory
instanceKlass org/gradle/api/tasks/OutputDirectories
instanceKlass org/gradle/api/tasks/options/OptionValues
instanceKlass org/gradle/api/tasks/Nested
instanceKlass org/gradle/api/tasks/LocalState
instanceKlass org/gradle/api/tasks/InputFiles
instanceKlass org/gradle/api/tasks/InputFile
instanceKlass org/gradle/api/tasks/InputDirectory
instanceKlass org/gradle/api/artifacts/transform/InputArtifactDependencies
instanceKlass org/gradle/api/artifacts/transform/InputArtifact
instanceKlass org/gradle/api/tasks/Input
instanceKlass org/gradle/api/tasks/Destroys
instanceKlass org/gradle/api/tasks/Console
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadataStore
instanceKlass org/gradle/internal/execution/TaskExecutionTracker
instanceKlass org/gradle/api/internal/project/taskfactory/TaskClassInfoStore
instanceKlass org/gradle/internal/execution/WorkInputListeners
instanceKlass org/gradle/internal/properties/annotations/TypeAnnotationHandler
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices
instanceKlass org/gradle/tooling/internal/provider/ExecuteBuildActionRunner
instanceKlass org/gradle/internal/buildtree/BuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderCache
instanceKlass org/gradle/internal/buildevents/BuildLoggerFactory
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGlobalScopeServices
instanceKlass org/gradle/internal/buildoption/IntegerInternalOption
instanceKlass org/gradle/internal/buildoption/InternalFlag
instanceKlass org/gradle/internal/buildoption/InternalOption
instanceKlass org/gradle/internal/buildoption/Option
instanceKlass org/gradle/internal/service/DefaultServiceLocator$ServiceFactory
instanceKlass org/gradle/internal/service/scopes/AbstractPluginServiceRegistry
instanceKlass org/gradle/internal/service/scopes/PluginServiceRegistry
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/internal/InternalTransformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass com/google/common/collect/Sets
instanceKlass groovy/lang/MetaClass
instanceKlass groovy/lang/MetaObjectProtocol
instanceKlass groovy/lang/GroovySystem
instanceKlass groovy/lang/MetaClassRegistry
instanceKlass groovy/lang/GroovyObject
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass java/util/ComparableTimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass org/gradle/internal/util/Trie$Builder
instanceKlass org/gradle/internal/util/Trie
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$TrieSet
instanceKlass jdk/internal/loader/BootLoader$PackageHelper
instanceKlass java/util/stream/StreamSpliterators
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass java/util/function/IntFunction
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$Java9PackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$AbstractClassLoaderLookuper
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassLoaderPackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassDefiner
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils
instanceKlass org/gradle/initialization/GradleApiSpecAggregator$DefaultSpec
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/math/IntMath$1
instanceKlass com/google/common/math/MathPreconditions
instanceKlass com/google/common/math/IntMath
instanceKlass com/google/common/base/Preconditions
instanceKlass org/apache/groovy/json/DefaultFastStringServiceFactory
instanceKlass org/apache/groovy/json/FastStringServiceFactory
instanceKlass org/gradle/internal/reflect/ReflectionCache$CacheEntry
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass org/gradle/kotlin/dsl/provider/KotlinGradleApiSpecProvider
instanceKlass org/gradle/initialization/GradleApiSpecProvider$SpecAdapter
instanceKlass org/gradle/initialization/GradleApiSpecProvider
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/initialization/GradleApiSpecProvider$Spec
instanceKlass org/gradle/initialization/GradleApiSpecAggregator
instanceKlass com/google/common/base/Function
instanceKlass org/gradle/internal/reflect/CachedInvokable
instanceKlass org/gradle/internal/reflect/ReflectionCache
instanceKlass org/gradle/internal/reflect/DirectInstantiator
instanceKlass org/gradle/initialization/DefaultClassLoaderRegistry
instanceKlass org/gradle/internal/installation/GradleRuntimeShadedJarDetector
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLClassLoader$2
instanceKlass org/objectweb/asm/Type
instanceKlass org/gradle/initialization/DefaultLegacyTypesSupport
instanceKlass org/gradle/api/internal/DynamicModulesClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/DefaultPluginModuleRegistry
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList$Builder
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/properties/annotations/AbstractPropertyAnnotationHandler
instanceKlass org/gradle/internal/properties/annotations/PropertyAnnotationHandler
instanceKlass org/gradle/internal/instantiation/InjectAnnotationHandler
instanceKlass org/gradle/api/tasks/util/PatternSet
instanceKlass org/gradle/api/tasks/util/PatternFilterable
instanceKlass org/gradle/api/tasks/AntBuilderAware
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractionStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractionStrategy
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractor
instanceKlass org/gradle/internal/event/DefaultListenerManager
instanceKlass org/gradle/api/internal/DocumentationRegistry
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler
instanceKlass org/gradle/internal/remote/internal/inet/InetAddressFactory
instanceKlass org/gradle/cache/internal/locklistener/FileLockContentionHandler
instanceKlass org/gradle/api/internal/file/FileLookup
instanceKlass org/gradle/internal/state/ManagedFactoryRegistry
instanceKlass org/gradle/api/internal/file/DefaultFilePropertyFactory
instanceKlass org/gradle/api/internal/file/FileFactory
instanceKlass org/gradle/api/internal/file/FileResolver
instanceKlass org/gradle/internal/file/PathToFileResolver
instanceKlass org/gradle/internal/file/RelativeFilePathResolver
instanceKlass org/gradle/api/internal/provider/PropertyHost
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator
instanceKlass org/gradle/internal/state/ManagedFactory
instanceKlass org/gradle/api/internal/tasks/TaskDependencyFactory
instanceKlass org/gradle/api/internal/file/FilePropertyFactory
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector
instanceKlass org/gradle/execution/DefaultWorkValidationWarningRecorder
instanceKlass org/gradle/execution/WorkValidationWarningReporter
instanceKlass org/gradle/internal/execution/steps/ValidateStep$ValidationWarningRecorder
instanceKlass org/gradle/api/internal/cache/StringInterner
instanceKlass com/google/common/collect/Interner
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/internal/operations/CurrentBuildOperationRef
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractor
instanceKlass org/gradle/model/internal/inspect/ModelRuleExtractor
instanceKlass org/gradle/model/internal/manage/instance/ManagedProxyFactory
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/internal/instantiation/InstanceGenerator
instanceKlass org/gradle/internal/environment/GradleBuildEnvironment
instanceKlass org/gradle/api/internal/collections/DomainObjectCollectionFactory
instanceKlass org/gradle/internal/service/scopes/GradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/operations/BuildOperationProgressEventEmitter
instanceKlass org/gradle/internal/operations/BuildOperationListenerManager
instanceKlass org/gradle/internal/instantiation/InstantiatorFactory
instanceKlass org/gradle/internal/instantiation/PropertyRoleAnnotationHandler
instanceKlass org/gradle/api/model/ObjectFactory
instanceKlass org/gradle/internal/reflect/Instantiator
instanceKlass org/gradle/api/internal/classpath/PluginModuleRegistry
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryInfo
instanceKlass org/gradle/initialization/ClassLoaderRegistry
instanceKlass org/gradle/internal/execution/history/OverlappingOutputDetector
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/model/internal/manage/binding/StructBindingsStore
instanceKlass org/gradle/internal/execution/history/changes/ExecutionStateChangeDetector
instanceKlass org/gradle/api/tasks/util/internal/PatternSpecFactory
instanceKlass org/gradle/configuration/ImportsReader
instanceKlass org/gradle/initialization/JdkToolsInitializer
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/model/internal/manage/schema/ModelSchemaStore
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractor
instanceKlass org/gradle/cache/internal/InMemoryCacheDecoratorFactory
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/process/internal/health/memory/OsMemoryInfo
instanceKlass org/gradle/process/internal/health/memory/MemoryManager
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCacheFactory
instanceKlass org/gradle/internal/operations/BuildOperationIdFactory
instanceKlass org/gradle/initialization/LegacyTypesSupport
instanceKlass org/gradle/api/internal/provider/PropertyFactory
instanceKlass org/gradle/internal/logging/progress/ProgressLoggerFactory
instanceKlass org/gradle/internal/logging/progress/ProgressListener
instanceKlass org/gradle/internal/hash/StreamHasher
instanceKlass org/gradle/cache/internal/CacheFactory
instanceKlass org/gradle/internal/file/Deleter
instanceKlass org/gradle/api/internal/file/collections/DirectoryFileTreeFactory
instanceKlass org/gradle/internal/jvm/inspection/JvmVersionDetector
instanceKlass org/gradle/api/internal/file/FileCollectionFactory
instanceKlass org/gradle/internal/jvm/inspection/JvmMetadataDetector
instanceKlass java/util/function/BiConsumer
instanceKlass org/gradle/cache/internal/ProcessMetaDataProvider
instanceKlass org/gradle/process/internal/ExecFactory
instanceKlass org/gradle/api/internal/ProcessOperations
instanceKlass org/gradle/process/internal/JavaForkOptionsFactory
instanceKlass org/gradle/process/internal/JavaExecHandleFactory
instanceKlass org/gradle/process/internal/ExecHandleFactory
instanceKlass org/gradle/process/internal/ExecActionFactory
instanceKlass org/gradle/internal/service/scopes/BasicGlobalScopeServices
instanceKlass org/gradle/cache/FileLockManager
instanceKlass org/gradle/launcher/daemon/registry/DaemonDir
instanceKlass org/gradle/internal/concurrent/Synchronizer
instanceKlass org/gradle/cache/internal/CacheSupport
instanceKlass org/gradle/cache/internal/CacheAccessSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry
instanceKlass org/gradle/cache/Cache
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices
instanceKlass org/gradle/internal/invocation/BuildAction
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandAction
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass org/gradle/internal/concurrent/ExecutorFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy
instanceKlass org/gradle/launcher/daemon/server/MasterExpirationStrategy
instanceKlass org/gradle/launcher/exec/BuildExecuter
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthStats
instanceKlass org/gradle/launcher/daemon/context/DaemonContext
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthCheck
instanceKlass org/gradle/launcher/daemon/server/health/HealthExpirationStrategy
instanceKlass org/gradle/internal/event/ListenerManager
instanceKlass org/gradle/launcher/daemon/server/stats/DaemonRunningStats
instanceKlass org/gradle/launcher/daemon/server/Daemon
instanceKlass org/gradle/internal/serialize/Serializer
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CompositeServiceProvider
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ParentServices
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/launcher/exec/BuildActionExecuter
instanceKlass org/gradle/launcher/daemon/server/scaninfo/DaemonScanInfo
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/DaemonServerConnector
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingSystem
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingRouter
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/Slf4jLoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter
instanceKlass org/gradle/internal/logging/LoggingManagerInternal
instanceKlass org/gradle/internal/logging/StandardOutputCapture
instanceKlass org/gradle/api/logging/LoggingManager
instanceKlass org/gradle/internal/logging/source/StdErrLoggingSystem
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$OutputEventDestination
instanceKlass org/gradle/internal/SystemProperties
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$1
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler
instanceKlass org/gradle/internal/logging/events/operations/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/io/TextStream
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem
instanceKlass org/gradle/internal/logging/source/StdOutLoggingSystem
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager$1
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManagerFactory
instanceKlass org/gradle/internal/logging/services/TextStreamOutputEventListener
instanceKlass org/gradle/internal/logging/services/LoggingServiceRegistry$1
instanceKlass org/gradle/internal/logging/config/LoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSourceSystem
instanceKlass org/gradle/launcher/daemon/configuration/DefaultDaemonServerConfiguration
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorage
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibrary
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory$1
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/stream/FindOps
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/function/Predicate
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLogger
instanceKlass net/rubygrapefruit/platform/file/FileEvents
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLibraryFunctions
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter
instanceKlass net/rubygrapefruit/platform/internal/LibraryDef
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLocator
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLoader
instanceKlass net/rubygrapefruit/platform/Process
instanceKlass net/rubygrapefruit/platform/internal/Platform
instanceKlass net/rubygrapefruit/platform/Native
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/gradle/api/internal/file/temp/DefaultTemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$1
instanceKlass org/gradle/internal/file/StatStatistics
instanceKlass org/gradle/internal/file/StatStatistics$Collector
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem
instanceKlass org/gradle/internal/service/InjectUtil
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$2
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$1
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileCanonicalizer
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem$Factory
instanceKlass org/gradle/api/internal/file/temp/TemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileSystem
instanceKlass org/gradle/internal/file/Stat
instanceKlass org/gradle/internal/file/Chmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeMutator
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeAccessor
instanceKlass org/gradle/internal/nativeintegration/filesystem/Symlink
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FileSystemServices
instanceKlass org/gradle/internal/nativeintegration/jansi/DefaultJansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorageLocator
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiBootPathConfigurer
instanceKlass java/lang/Class$3
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector$ClassDetails
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/gradle/internal/reflect/JavaMethod
instanceKlass org/gradle/internal/service/AbstractServiceMethod
instanceKlass java/util/LinkedList$ListItr
instanceKlass net/rubygrapefruit/platform/WindowsRegistry
instanceKlass org/gradle/internal/os/OperatingSystem
instanceKlass net/rubygrapefruit/platform/file/PosixFiles
instanceKlass net/rubygrapefruit/platform/file/Files
instanceKlass net/rubygrapefruit/platform/memory/Memory
instanceKlass net/rubygrapefruit/platform/SystemInfo
instanceKlass net/rubygrapefruit/platform/file/FileSystems
instanceKlass org/gradle/internal/jvm/Jvm
instanceKlass org/gradle/internal/jvm/JavaInfo
instanceKlass org/gradle/internal/service/RelevantMethodsBuilder
instanceKlass org/gradle/internal/Cast
instanceKlass org/gradle/internal/service/ServiceMethod
instanceKlass org/gradle/internal/service/MethodHandleBasedServiceMethodFactory
instanceKlass org/gradle/internal/service/DefaultServiceMethodFactory
instanceKlass org/gradle/internal/service/ServiceMethodFactory
instanceKlass org/gradle/internal/service/RelevantMethods
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ThisAsService
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$1
instanceKlass org/gradle/internal/concurrent/CompositeStoppable
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler$Registration
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$OwnServices
instanceKlass org/gradle/internal/nativeintegration/ProcessEnvironment
instanceKlass net/rubygrapefruit/platform/ProcessLauncher
instanceKlass net/rubygrapefruit/platform/NativeIntegration
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleDetector
instanceKlass org/gradle/initialization/GradleUserHomeDirProvider
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileMetadataAccessor
instanceKlass org/gradle/internal/nativeintegration/network/HostnameLookup
instanceKlass org/gradle/internal/nativeintegration/NativeCapabilities
instanceKlass org/gradle/internal/service/ServiceRegistration
instanceKlass org/gradle/internal/service/ServiceProvider$Visitor
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ManagedObjectServiceProvider
instanceKlass org/gradle/internal/service/Service
instanceKlass org/gradle/internal/service/ServiceProvider
instanceKlass org/gradle/internal/concurrent/Stoppable
instanceKlass org/gradle/internal/service/DefaultServiceRegistry
instanceKlass org/gradle/internal/service/ContainsServices
instanceKlass org/gradle/internal/serialize/AbstractDecoder
instanceKlass org/gradle/internal/serialize/Decoder
instanceKlass org/gradle/launcher/bootstrap/EntryPoint$RecordingExecutionListener
instanceKlass org/gradle/internal/logging/events/operations/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/slf4j/BuildOperationAwareLogger
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$2
instanceKlass org/gradle/internal/dispatch/ReflectionDispatch
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$1
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$LazyListener
instanceKlass org/gradle/internal/logging/events/OutputEventListener$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass java/util/function/Consumer
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass java/lang/reflect/Proxy
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter$DispatchingInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter
instanceKlass org/gradle/internal/logging/events/operations/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/sink/OutputEventTransformer
instanceKlass org/gradle/internal/exceptions/MultiCauseException
instanceKlass org/gradle/internal/event/AbstractBroadcastDispatch
instanceKlass org/gradle/internal/event/ListenerBroadcast
instanceKlass org/gradle/internal/dispatch/Dispatch
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleMetaData
instanceKlass org/gradle/internal/logging/console/ColorMap
instanceKlass org/gradle/internal/logging/text/StyledTextOutput
instanceKlass org/gradle/internal/logging/format/LogHeaderFormatter
instanceKlass org/gradle/internal/Factory
instanceKlass org/gradle/api/logging/StandardOutputListener
instanceKlass org/gradle/internal/logging/config/LoggingSystem$Snapshot
instanceKlass org/gradle/internal/logging/events/OutputEvent
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer
instanceKlass org/gradle/internal/logging/config/LoggingRouter
instanceKlass org/gradle/internal/logging/LoggingOutputInternal
instanceKlass org/gradle/api/logging/LoggingOutput
instanceKlass org/gradle/internal/logging/config/LoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext$NoOpLogger
instanceKlass org/gradle/api/logging/Logger
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/gradle/internal/time/TimeSource$1
instanceKlass org/gradle/internal/time/TimeSource
instanceKlass org/gradle/internal/time/MonotonicClock
instanceKlass org/gradle/internal/time/CountdownTimer
instanceKlass org/gradle/internal/time/Timer
instanceKlass org/gradle/internal/time/Clock
instanceKlass org/gradle/internal/time/Time
instanceKlass org/gradle/internal/logging/events/OutputEventListener
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/slf4j/helpers/BasicMarker
instanceKlass org/slf4j/Marker
instanceKlass org/slf4j/helpers/BasicMarkerFactory
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/MarkerFactory
instanceKlass org/gradle/api/logging/Logging
instanceKlass org/gradle/launcher/daemon/configuration/DaemonServerConfiguration
instanceKlass org/gradle/internal/service/ServiceRegistry
instanceKlass org/gradle/internal/service/ServiceLookup
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/logging/text/StyledTextOutputFactory
instanceKlass org/gradle/api/logging/configuration/LoggingConfiguration
instanceKlass org/gradle/initialization/BuildClientMetaData
instanceKlass org/gradle/launcher/bootstrap/ExecutionCompleter
instanceKlass org/gradle/launcher/bootstrap/ExecutionListener
instanceKlass org/gradle/launcher/bootstrap/EntryPoint
instanceKlass java/io/FilePermissionCollection$1
instanceKlass java/security/Security$2
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/util/LinkedList$Node
instanceKlass java/nio/charset/CoderResult
instanceKlass java/net/URLClassLoader$1
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/IoActions
instanceKlass java/util/Properties$LineReader
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass org/gradle/internal/InternalTransformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/Collections$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/Cast
instanceKlass java/util/AbstractList$Itr
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList$Builder
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass java/io/FileFilter
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/launcher/bootstrap/ProcessBootstrap
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass org/gradle/launcher/daemon/bootstrap/GradleDaemon
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/loader/Resource
instanceKlass java/util/StringTokenizer
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass java/security/CodeSigner
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/net/URI$Parser
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass java/lang/StringCoding
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/Void
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass java/util/function/Function
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 3 8 1 100 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1611 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 100 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 100 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 100 1 9 12 1 1 9 12 1 100 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 100 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 100 1 8 1 10 7 1 4 10 10 12 11 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 100 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 10 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 100 1 100 1 100 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 1 1 1 1 1 1 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 487 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 1 100 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/System 1 1 803 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/PipedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
staticfield java/lang/System err Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$RetrieveSystemPackagesClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1098 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 10 12 1 1 10 12 1 1 100 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 100 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/security/AccessController 1 1 295 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/GeneratedMethodAccessor1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass java/lang/Module 1 1 959 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 7 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 4 7 1 11 12 1 7 1 7 1 10 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 100 1 10 10 12 1 1 11 100 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 100 1 8 1 100 1 10 100 1 100 1 3 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 100 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 492 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 10 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 100 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 100 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 100 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 100 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 100 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 100 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 100 1 100 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 12
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/lang/String 1 1 1396 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 3 10 7 12 1 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 100 1 100 1 8 1 10 10 10 12 1 8 1 10 12 1 3 3 7 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 1 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 8 1 8 1 10 8 1 8 1 8 1 8 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/security/ProtectionDomain 1 1 324 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 1 10 100 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 100 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 395 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 100 1 10 12 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 10 12 10 12 1 1 11 100 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 11 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 409 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1285 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 100 1 9 7 1 9 100 1 9 9 100 1 9 100 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass java/lang/ThreadGroup 1 1 293 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 100 10 12 1 1 10 7 12 1 1 1 10 100 12 1 9 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 7 10 12 1 9 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 1 100 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 8 1 10 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 651 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 10 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 100 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 8 1 10 100 1 11 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 100 1 10 11 100 12 1 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 512 100 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 100 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 100 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 10 12 4 10 12 1 8 1 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/io/PipedInputStream
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection$SocketInputStream
instanceKlass org/gradle/internal/io/RandomAccessFileInputStream
instanceKlass com/esotericsoftware/kryo/io/Input
instanceKlass org/gradle/internal/serialize/kryo/KryoBackedDecoder$1
instanceKlass org/gradle/internal/serialize/AbstractDecoder$DecoderStream
instanceKlass org/gradle/internal/stream/EncodedStream$EncodedInput
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 184 100 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 100 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer$ForwardEvents
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 612 9 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 1 3 8 1 100 1 5 0 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 100 1 8 1 10 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 100 1 10 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 100 1 11 7 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 10 12 9 12 1 1 10 9 12 1 10 12 1 100 1 10 10 12 1 1 9 12 1 10 12 1 11 100 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 10 12 1 10 12 8 1 10 12 1 8 1 10 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 1 9 12 1 10 12 1 1 100 1 10 12 11 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 11 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 8 1 9 12 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 10 12 1 7 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 2048 0 1024 0 -1
ciInstanceKlass java/lang/StringLatin1 1 1 380 7 1 10 100 12 1 1 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 8 1 10 12 100 1 10 10 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 395 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 5 0 5 0 100 1 3 5 0 3 5 0 10 12 1 10 12 1 8 1 10 12 1 8 1 9 12 1 1 9 12 1 10 12 1 1 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 1 1 10 12 10 12 1 4 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 1 9 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 1024 880 2295 0 -1
ciMethod java/lang/StringLatin1 hashCode ([B)I 1846 68538 1963 0 352
ciMethod java/lang/StringLatin1 indexOf ([BII)I 1024 0 5730 0 448
ciMethod java/lang/StringLatin1 indexOfChar ([BIII)I 618 8192 2452 0 -1
ciMethod java/lang/StringLatin1 charAt ([BI)C 1024 0 666921 0 128
ciMethod java/lang/StringLatin1 canEncode (I)Z 1024 0 89503 0 96
ciInstanceKlass java/lang/StringUTF16 1 1 598 100 1 7 1 10 100 12 1 1 1 100 1 10 7 1 3 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciMethod java/lang/StringUTF16 hashCode ([B)I 0 0 1 0 0
ciMethod java/lang/StringUTF16 indexOf ([BII)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 checkIndex (I[B)V 2 0 63 0 -1
ciMethod java/lang/StringUTF16 getChar ([BI)C 1024 0 10139 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 2 0 7 0 0
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 393 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 100 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass com/google/common/collect/RegularImmutableMap$BucketOverflowException
instanceKlass sun/security/pkcs11/wrapper/PKCS11Exception
instanceKlass java/security/GeneralSecurityException
instanceKlass java/text/ParseException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/lang/InterruptedException
instanceKlass java/net/URISyntaxException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/api/internal/provider/AbstractProperty$PropertyQueryException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/gradle/internal/reflect/NoSuchPropertyException
instanceKlass org/gradle/internal/typeconversion/TypeConversionException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass org/gradle/internal/work/NoAvailableWorkerLeaseException
instanceKlass org/gradle/launcher/daemon/server/BadlyFormedRequestException
instanceKlass java/security/ProviderException
instanceKlass org/gradle/internal/remote/internal/MessageIOException
instanceKlass org/gradle/cache/InsufficientLockModeException
instanceKlass org/gradle/cache/LockTimeoutException
instanceKlass org/gradle/cache/internal/locklistener/GracefullyStoppedException
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry$EmptyRegistryException
instanceKlass org/gradle/cache/FileIntegrityViolationException
instanceKlass org/gradle/internal/file/FileException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStoppedException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonUnavailableException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/util/MissingResourceException
instanceKlass org/gradle/util/internal/GFileUtils$TailReadingException
instanceKlass org/gradle/internal/jvm/JavaHomeException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass java/util/NoSuchElementException
instanceKlass org/gradle/api/reflect/ObjectInstantiationException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass org/gradle/internal/nativeintegration/NativeIntegrationException
instanceKlass org/gradle/internal/reflect/NoSuchMethodException
instanceKlass net/rubygrapefruit/platform/NativeException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass com/esotericsoftware/kryo/KryoException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/gradle/internal/operations/BuildOperationInvocationException
instanceKlass org/gradle/internal/UncheckedException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/SecurityException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/IllegalStateException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/security/InvalidParameterException
instanceKlass java/lang/NumberFormatException
instanceKlass org/gradle/internal/service/UnknownServiceException
instanceKlass org/gradle/internal/service/UnknownServiceException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 224 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 100 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass java/util/TreeMap$Values
instanceKlass com/google/common/collect/ImmutableCollection
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/HashMap$Values
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/AbstractQueue
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 151 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 100 12 1 1 1 9 100 12 1 1 1 9 100 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/Character 1 1 576 7 1 100 1 100 1 9 12 1 1 8 1 9 12 1 1 100 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/Float 1 1 223 7 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 4 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 4 4 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/math/BigDecimal
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Double 1 1 285 7 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 6 0 6 0 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 215 7 1 100 1 10 100 12 1 1 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 224 7 1 100 1 100 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 445 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 3 3 3 3 3 3 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 506 7 1 100 1 7 1 100 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 195 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 12 1 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 47 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 152 9 7 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 12 1 100 1 11 100 12 1 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 398 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 7 12 1 1 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 100 1 10 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 11 100 1 100 1 8 1 10 10 12 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 9 12 1 100 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 548 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 12 1 8 1 11 100 12 1 1 1 100 1 10 12 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 10 12 1 100 1 8 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 12 16 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 429 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 100 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 446 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 100 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 437 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 100 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 100 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 100 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 470 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 10 100 12 1 1 1 10 10 12 1 10 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 547 7 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 100 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 10 12 1 10 8 1 8 1 8 1 10 10 100 1 10 12 1 100 1 10 100 1 10 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 15 10 12 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 1 10 100 1 10 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 100 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 100 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 373 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/net/URL 1 1 743 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 100 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 9 12 1 1 10 7 12 1 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 8 9 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 336 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 10 12 1 11 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/google/common/cache/LocalCache
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass java/util/EnumMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 100 1 100 1 11 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1
instanceKlass sun/security/jca/ProviderList$3
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Vector
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 100 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 100 1 10 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 100 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 92 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 302 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 10 12 10 12 1 1 100 1 100 1 100 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 771 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 100 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass jdk/net/UnixDomainPrincipal
ciInstanceKlass java/lang/Record 0 0 22 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 224 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 100 1 10 10 100 12 1 1 1 10 11 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 644 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 1 9 12 1 1 100 1 10 9 100 12 1 1 1 9 100 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 100 1 11 12 1 100 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 940 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 8 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1052 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 9 12 1 10 12 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 7 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 1 8 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 10 12 1 1 8 1 8 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 100 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 684 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 100 12 1 1 1 8 1 10 100 12 1 1 1 100 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 59 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeQualifiedFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 254 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 8 1 8 1 8 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldStaticReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 390 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 100 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 8 10 12 1 1 7 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 757 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 10 12 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 7 1 7 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 100 1 8 9 100 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 3 100 1 10 12 1 10 7 12 1 1 1 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 100 1 10 12 1 10 100 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 100 1 10 100 12 1 1 1 9 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 10 10 100 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 1 100 1 3 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 9 100 12 1 1 1 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 100 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 7 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 100 1 10 18 12 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/org/objectweb/asm/Type 1 1 361 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 9 100 1 9 12 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 100 1 10 8 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/org/objectweb/asm/Type VOID_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type BOOLEAN_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type CHAR_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type BYTE_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type SHORT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type INT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type FLOAT_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type LONG_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
staticfield jdk/internal/org/objectweb/asm/Type DOUBLE_TYPE Ljdk/internal/org/objectweb/asm/Type; jdk/internal/org/objectweb/asm/Type
ciInstanceKlass jdk/internal/org/objectweb/asm/Label 1 1 220 10 7 12 1 1 1 9 7 12 1 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 3 10 12 1 1 3 9 7 12 1 1 10 12 1 1 10 12 1 1 3 10 12 1 9 12 1 3 3 100 1 100 1 100 1 9 12 1 9 12 1 9 12 1 10 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 12 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/org/objectweb/asm/Label EMPTY_LIST Ljdk/internal/org/objectweb/asm/Label; jdk/internal/org/objectweb/asm/Label
ciInstanceKlass jdk/internal/org/objectweb/asm/SymbolTable 1 1 594 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 7 1 10 9 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 7 1 10 12 1 1 10 12 1 100 1 8 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 3 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 9 12 1 1 7 1 10 12 1 10 12 1 1 7 1 10 7 1 10 12 1 1 7 1 10 7 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/org/objectweb/asm/SymbolTable$Entry 1 1 39 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable$Entry
ciInstanceKlass jdk/internal/org/objectweb/asm/Symbol 1 1 92 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 7 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1
ciMethod jdk/internal/org/objectweb/asm/SymbolTable get (I)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 530 0 13979 0 160
ciMethod jdk/internal/org/objectweb/asm/SymbolTable put (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 694 0 5527 0 2208
ciMethod jdk/internal/org/objectweb/asm/SymbolTable hash (ILjava/lang/String;)I 772 0 11629 0 0
ciMethod jdk/internal/org/objectweb/asm/SymbolTable hash (ILjava/lang/String;I)I 298 0 149 0 -1
ciMethod jdk/internal/org/objectweb/asm/SymbolTable addTypeInternal (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)I 236 0 799 0 0
ciMethod jdk/internal/org/objectweb/asm/SymbolTable addType (Ljava/lang/String;)I 276 22 2724 0 0
ciMethod jdk/internal/org/objectweb/asm/SymbolTable addUninitializedType (Ljava/lang/String;I)I 14 2 149 0 0
ciInstanceKlass jdk/internal/org/objectweb/asm/Frame 1 1 467 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 100 1 3 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 3 8 1 7 1 9 12 1 10 7 12 1 1 1 3 10 12 1 1 10 12 1 1 3 3 3 3 10 12 1 10 12 1 1 3 3 3 3 100 1 10 100 1 100 1 3 10 12 1 3 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 3 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 3 10 12 1 10 12 1 3 3 3 3 10 12 1 1 9 7 12 1 1 1 3 9 12 1 8 1 8 8 1 8 1 10 12 1 100 1 10 10 12 1 1 10 12 10 12 1 1 3 8 1 10 12 1 10 12 9 12 1 10 12 1 3 3 3 3 3 3 3 3 100 1 10 10 12 1 1 10 12 1 10 12 1 3 10 12 1 10 12 1 1 10 12 1 1 3 10 12 1 8 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
ciMethod jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V 540 0 5449 0 -1
ciMethod jdk/internal/org/objectweb/asm/Frame push (I)V 410 0 4296 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;)V 128 0 1286 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 128 0 1152 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame pop ()I 186 0 1938 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame pop (I)V 194 0 1901 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame getAbstractTypeFromDescriptor (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;I)I 526 0 1790 0 -1
ciMethod jdk/internal/org/objectweb/asm/Frame getLocal (I)I 136 0 1596 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame setLocal (II)V 62 0 686 0 0
ciMethod jdk/internal/org/objectweb/asm/Frame addInitializedType (I)V 16 0 167 0 0
ciMethod jdk/internal/org/objectweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 516 0 5378 0 512
ciMethod jdk/internal/org/objectweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;JI)V 230 0 115 0 -1
ciMethod jdk/internal/org/objectweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 648 0 5461 0 800
ciMethod jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 512 594 1824 0 0
ciMethod jdk/internal/org/objectweb/asm/Type getReturnTypeOffset (Ljava/lang/String;)I 502 750 980 0 0
ciMethod java/lang/Math max (II)I 776 0 134800 0 -1
ciMethod java/lang/String charAt (I)C 1024 0 682671 0 160
ciMethod java/lang/String length ()I 1024 0 257920 0 -1
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 1024 0 5865 0 416
ciMethod java/lang/String hashCode ()I 1024 0 10945 0 512
ciMethod java/lang/String indexOf (II)I 1024 0 101276 0 512
ciMethod java/lang/String isLatin1 ()Z 1024 0 853115 0 96
ciMethod java/lang/String substring (II)Ljava/lang/String; 512 0 5447 0 -1
ciMethod java/lang/Object <init> ()V 4120 0 181747 0 64
ciInstanceKlass java/lang/AssertionError 0 0 79 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 181747 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 hashCode ([B)I 2 60426 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xd0007 0x412 0x38 0x662c 0x250003 0x662d 0xffffffffffffffe0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String hashCode ()I 2 10945 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x60007 0x1db5 0x108 0xb0d 0xd0007 0x33 0xe8 0xada 0x110005 0xada 0x0 0x0 0x0 0x0 0x0 0x8000000600140007 0x1 0x48 0xada 0x1b0002 0xada 0x1e0003 0xada 0x28 0x250002 0x1 0x2a0007 0xada 0x38 0x1 0x320003 0x1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/String isLatin1 ()Z 2 853115 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0xd027c 0xa0007 0x6 0x38 0xd0276 0xe0003 0xd0276 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 5865 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x128a 0x20 0x260 0x80104 0x0 0x0 0x2aa65acd580 0x1287 0x0 0x0 0xb0007 0x3 0xe0 0x1287 0xf0004 0x0 0x0 0x2aa65acd580 0x1287 0x0 0x0 0x160007 0x0 0x40 0x1287 0x210007 0x0 0x68 0x1287 0x2c0002 0x1287 0x2f0007 0xd76 0x38 0x511 0x330003 0x511 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/lang/String charAt (I)C 2 682671 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10005 0xa68b0 0x0 0x0 0x0 0x0 0x0 0x40007 0x3 0x30 0xa68ad 0xc0002 0xa68ad 0x150002 0x3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 666921 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10007 0x0 0x40 0xa2b2a 0x70007 0xa2b2a 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 canEncode (I)Z 2 89503 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x8000000600040007 0x2 0x38 0x15b9f 0x80003 0x15b9f 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 hashCode ([B)I 1 17 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0xb0007 0x1 0x48 0x11 0x140002 0x11 0x1c0003 0x11 0xffffffffffffffd0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 charAt ([BI)C 1 7 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x20002 0x6 0x70002 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String indexOf (II)I 2 101276 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10005 0x1899d 0x0 0x0 0x0 0x0 0x0 0x40007 0x0 0x48 0x1899d 0xd0002 0x1899d 0x100003 0x1899d 0x28 0x190002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 indexOf ([BII)I 2 5730 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10002 0x1463 0x40007 0x1463 0x20 0x0 0xd0007 0x1463 0x38 0x0 0x120003 0x0 0x38 0x170007 0x1433 0x20 0x30 0x200002 0x1433 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable get (I)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 2 13979 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 2 5378 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x70002 0x1400 0x0 0x0 0x0 0x0 0x9 0x5 0x7e 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 2 5461 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10002 0x1411 0x0 0x0 0x0 0x0 0x9 0x8 0x3e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable hash (ILjava/lang/String;)I 2 11629 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x40005 0x2beb 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0xe oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable put (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 2 5527 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0xd0007 0x143a 0xc8 0x2 0x290007 0x2 0xa8 0x200 0x370007 0x200 0x70 0x182 0x5a0004 0x0 0x0 0x2aa6ac1d570 0x182 0x0 0x0 0x5f0003 0x182 0xffffffffffffffa8 0x650003 0x200 0xffffffffffffff70 0x940004 0x0 0x0 0x2aa6ac1d570 0x143c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x64 0x40 oops 2 15 jdk/internal/org/objectweb/asm/SymbolTable$Entry 28 jdk/internal/org/objectweb/asm/SymbolTable$Entry methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V 2 5457 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 1908 0x10008 0x192 0x0 0x3918 0x0 0xca0 0x3f 0xcb8 0x0 0xd08 0xc0 0xd08 0x3e 0xd08 0xc 0xd08 0x2 0xd08 0x2 0xd08 0x2 0xd08 0x0 0xd58 0x0 0xd58 0x0 0xde0 0x0 0xde0 0x0 0xde0 0x0 0xe30 0x0 0xe30 0x4 0xd08 0x0 0xd08 0x12a 0xeb8 0x0 0x3918 0x0 0x3918 0x29 0xd08 0x1b 0xd58 0x0 0xde0 0x0 0xe30 0x483 0x13f8 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x2688 0x0 0x1480 0x0 0x27d0 0x0 0x1540 0xf 0x1600 0x0 0x2688 0x0 0x2688 0x0 0x2688 0x0 0x16f8 0x11 0x18e0 0x0 0x16f8 0x0 0x18e0 0x1a1 0x16f8 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x3918 0x0 0x1b38 0x0 0x1b88 0x0 0x1b38 0x0 0x1b88 0x44 0x1b38 0x0 0x1b38 0x0 0x1b38 0x0 0x1b38 0x4 0x1bd8 0x0 0x1c28 0xf2 0x1c78 0x0 0x1d38 0x0 0x1e68 0x0 0x2008 0x0 0x2170 0x0 0x2348 0x0 0x2590 0x0 0x2688 0x0 0x2710 0x0 0x27d0 0x0 0x2858 0x0 0x2688 0x0 0x2710 0x0 0x27d0 0x0 0x2858 0x0 0x2688 0x0 0x2710 0x0 0x27d0 0x0 0x2858 0x0 0x2688 0x0 0x2710 0x0 0x27d0 0x0 0x2858 0x0 0x2688 0x0 0x2710 0x0 0x27d0 0x0 0x2858 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0x2688 0x0 0x2918 0x0 0x2688 0x0 0x2918 0x0 0x2688 0x0 0x2918 0x0 0x2688 0x0 0x2710 0x0 0x2688 0x0 0x2710 0x0 0x2688 0x0 0x2710 0x0 0x29d8 0x0 0x2a28 0x0 0x2ae8 0x0 0x2b70 0x0 0x2688 0x0 0x27d0 0x0 0x1540 0x0 0x2c30 0x0 0x2a28 0x0 0x2b70 0x0 0x2688 0x0 0x1480 0x0 0x27d0 0x0 0xca0 0x0 0xca0 0x0 0xca0 0x0 0x2cb8 0x0 0x2688 0x0 0x2688 0x0 0x2cb8 0x0 0x2cb8 0x11 0x1bd8 0x0 0x1bd8 0x0 0x1bd8 0x0 0x1bd8 0x0 0x1bd8 0x0 0x1bd8 0x0 0x1c28 0x0 0x1c28 0x0 0x1c28 0x0 0x1c28 0x0 0x1c28 0x0 0x1c28 0x0 0x1c28 0x11 0x1c28 0x0 0xca0 0x0 0x2d40 0x0 0x2d40 0x0 0x1bd8 0x0 0x1bd8 0x28 0x1bd8 0x3 0x1c28 0x0 0x1bd8 0x0 0x1c28 0x6d 0x1bd8 0x80 0xca0 0x60 0x2d50 0xa1 0x2da0 0xba 0x2df0 0x0 0x2e78 0x12e 0x2f00 0x9f 0x2f00 0x12b 0x2f00 0xa1 0x2f00 0x0 0x3090 0x8e 0x3118 0x0 0x31a0 0x78 0x3508 0x0 0x2c30 0xe9 0x1bd8 0x183 0x3728 0x0 0x2c30 0x0 0x1bd8 0x0 0x1bd8 0x0 0x3918 0x0 0x3890 0x0 0x1bd8 0x0 0x1bd8 0x3300003 0x80 0x2c88 0x3360005 0x3f 0x0 0x0 0x0 0x0 0x0 0x3390003 0x3f 0x2c38 0x33f0005 0x13d 0x0 0x0 0x0 0x0 0x0 0x3420003 0x13d 0x2be8 0x3480005 0x1b 0x0 0x0 0x0 0x0 0x0 0x34e0005 0x1b 0x0 0x0 0x0 0x0 0x0 0x3510003 0x1b 0x2b60 0x3570005 0x0 0x0 0x0 0x0 0x0 0x0 0x35a0003 0x0 0x2b10 0x3600005 0x0 0x0 0x0 0x0 0x0 0x0 0x3660005 0x0 0x0 0x0 0x0 0x0 0x0 0x3690003 0x0 0x2a88 0x3700008 0x20 0x0 0x530 0x0 0x110 0x0 0x1e8 0x0 0x160 0x0 0x238 0x56 0x2c0 0xd4 0x348 0x0 0x530 0x0 0x530 0x0 0x530 0x0 0x530 0x0 0x530 0x0 0x530 0x0 0x458 0x0 0x3d0 0x0 0x4e0 0x3bf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c20003 0x0 0x2928 0x3c80005 0x0 0x0 0x0 0x0 0x0 0x0 0x3ce0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3d10003 0x0 0x28a0 0x3d70005 0x0 0x0 0x0 0x0 0x0 0x0 0x3da0003 0x0 0x2850 0x3e00005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e60005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e90003 0x0 0x27c8 0x3f30005 0x56 0x0 0x0 0x0 0x0 0x0 0x3f70005 0x56 0x0 0x0 0x0 0x0 0x0 0x3fa0003 0x56 0x2740 0x4040005 0xd4 0x0 0x0 0x0 0x0 0x0 0x4080005 0xd4 0x0 0x0 0x0 0x0 0x0 0x40b0003 0xd4 0x26b8 0x4150005 0x0 0x0 0x0 0x0 0x0 0x0 0x4190005 0x0 0x0 0x0 0x0 0x0 0x0 0x41c0003 0x0 0x2630 0x4260005 0x0 0x0 0x0 0x0 0x0 0x0 0x42a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x42d0003 0x0 0x25a8 0x4370005 0x0 0x0 0x0 0x0 0x0 0x0 0x43a0003 0x0 0x2558 0x4410002 0x0 0x4480005 0x483 0x0 0x0 0x0 0x0 0x0 0x44b0005 0x483 0x0 0x0 0x0 0x0 0x0 0x44e0003 0x483 0x24c0 0x4530005 0x0 0x0 0x0 0x0 0x0 0x0 0x4590005 0x0 0x0 0x0 0x0 0x0 0x0 0x45f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4620003 0x0 0x2400 0x4670005 0x0 0x0 0x0 0x0 0x0 0x0 0x46d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4730005 0x0 0x0 0x0 0x0 0x0 0x0 0x4760003 0x0 0x2340 0x47b0005 0xf 0x0 0x0 0x0 0x0 0x0 0x47f0005 0xf 0x0 0x0 0x0 0x0 0x0 0x4890007 0xf 0x38 0x0 0x48e0003 0x0 0x18 0x4960005 0xf 0x0 0x0 0x0 0x0 0x0 0x4990003 0xf 0x2248 0x49d0005 0x1a1 0x0 0x0 0x0 0x0 0x0 0x4a60005 0x1a1 0x0 0x0 0x0 0x0 0x0 0x4aa0007 0x62 0x21c0 0x13f 0x4b10005 0x13f 0x0 0x0 0x0 0x0 0x0 0x4ba0007 0x0 0x40 0x13f 0x4c10007 0x13f 0x70 0x0 0x4ca0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4cd0003 0x0 0x90 0x4d70007 0xbf 0x40 0x80 0x4e10007 0x80 0x58 0x0 0x4ed0005 0xbf 0x0 0x0 0x0 0x0 0x0 0x4f00003 0x13f 0x2060 0x4f50005 0x11 0x0 0x0 0x0 0x0 0x0 0x4f90005 0x11 0x0 0x0 0x0 0x0 0x0 0x5020005 0x11 0x0 0x0 0x0 0x0 0x0 0x50b0005 0x11 0x0 0x0 0x0 0x0 0x0 0x50f0007 0x0 0x1f68 0x11 0x5160005 0x11 0x0 0x0 0x0 0x0 0x0 0x51f0007 0x0 0x40 0x11 0x5260007 0x11 0x70 0x0 0x52f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5320003 0x0 0x90 0x53c0007 0x1 0x40 0x10 0x5460007 0x10 0x58 0x0 0x5520005 0x1 0x0 0x0 0x0 0x0 0x0 0x5550003 0x11 0x1e08 0x55a0005 0x44 0x0 0x0 0x0 0x0 0x0 0x55d0003 0x44 0x1db8 0x5620005 0x0 0x0 0x0 0x0 0x0 0x0 0x5650003 0x0 0x1d68 0x56a0005 0x193 0x0 0x0 0x0 0x0 0x0 0x56d0003 0x193 0x1d18 0x5720005 0x14 0x0 0x0 0x0 0x0 0x0 0x5750003 0x14 0x1cc8 0x5790005 0xf2 0x0 0x0 0x0 0x0 0x0 0x5810005 0xf2 0x0 0x0 0x0 0x0 0x0 0x5870005 0xf2 0x0 0x0 0x0 0x0 0x0 0x58a0003 0xf2 0x1c08 0x58e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5940005 0x0 0x0 0x0 0x0 0x0 0x0 0x59c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x5a80005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ab0003 0x0 0x1ad8 0x5af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5b50005 0x0 0x0 0x0 0x0 0x0 0x0 0x5bb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x5c90005 0x0 0x0 0x0 0x0 0x0 0x0 0x5cf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d50005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d80003 0x0 0x1938 0x5dc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5e20005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ea0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f00005 0x0 0x0 0x0 0x0 0x0 0x0 0x5f60005 0x0 0x0 0x0 0x0 0x0 0x0 0x5fc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5ff0003 0x0 0x17d0 0x6030005 0x0 0x0 0x0 0x0 0x0 0x0 0x6090005 0x0 0x0 0x0 0x0 0x0 0x0 0x60f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6170005 0x0 0x0 0x0 0x0 0x0 0x0 0x61d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6230005 0x0 0x0 0x0 0x0 0x0 0x0 0x6290005 0x0 0x0 0x0 0x0 0x0 0x0 0x62f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6320003 0x0 0x15f8 0x6360005 0x0 0x0 0x0 0x0 0x0 0x0 0x63c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6420005 0x0 0x0 0x0 0x0 0x0 0x0 0x6480005 0x0 0x0 0x0 0x0 0x0 0x0 0x6500005 0x0 0x0 0x0 0x0 0x0 0x0 0x6560005 0x0 0x0 0x0 0x0 0x0 0x0 0x65c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6620005 0x0 0x0 0x0 0x0 0x0 0x0 0x6680005 0x0 0x0 0x0 0x0 0x0 0x0 0x66e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6710003 0x0 0x13b0 0x6750005 0x0 0x0 0x0 0x0 0x0 0x0 0x67b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6830005 0x0 0x0 0x0 0x0 0x0 0x0 0x6890005 0x0 0x0 0x0 0x0 0x0 0x0 0x68c0003 0x0 0x12b8 0x6910005 0x0 0x0 0x0 0x0 0x0 0x0 0x6970005 0x0 0x0 0x0 0x0 0x0 0x0 0x69a0003 0x0 0x1230 0x69f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6a50005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ab0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ae0003 0x0 0x1170 0x6b30005 0x0 0x0 0x0 0x0 0x0 0x0 0x6b90005 0x0 0x0 0x0 0x0 0x0 0x0 0x6bc0003 0x0 0x10e8 0x6c10005 0x0 0x0 0x0 0x0 0x0 0x0 0x6c70005 0x0 0x0 0x0 0x0 0x0 0x0 0x6cd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6d00003 0x0 0x1028 0x6d50005 0x0 0x0 0x0 0x0 0x0 0x0 0x6db0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6e10005 0x0 0x0 0x0 0x0 0x0 0x0 0x6e40003 0x0 0xf68 0x6eb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ee0003 0x0 0xf18 0x6f30005 0x0 0x0 0x0 0x0 0x0 0x0 0x6f90005 0x0 0x0 0x0 0x0 0x0 0x0 0x6ff0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7020003 0x0 0xe58 0x7070005 0x0 0x0 0x0 0x0 0x0 0x0 0x70d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7100003 0x0 0xdd0 0x7150005 0x0 0x0 0x0 0x0 0x0 0x0 0x71b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7210005 0x0 0x0 0x0 0x0 0x0 0x0 0x7240003 0x0 0xd10 0x7290005 0x0 0x0 0x0 0x0 0x0 0x0 0x72f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7320003 0x0 0xc88 0x7370005 0x0 0x0 0x0 0x0 0x0 0x0 0x73d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7400003 0x0 0xc00 0x7490002 0x0 0x7540005 0x60 0x0 0x0 0x0 0x0 0x0 0x7570003 0x60 0xba0 0x75f0005 0xa1 0x0 0x0 0x0 0x0 0x0 0x7620003 0xa1 0xb50 0x7670005 0xba 0x0 0x0 0x0 0x0 0x0 0x7710005 0xba 0x0 0x0 0x0 0x0 0x0 0x7740003 0xba 0xac8 0x77c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7800005 0x0 0x0 0x0 0x0 0x0 0x0 0x7840003 0x0 0xa40 0x78c0005 0x399 0x0 0x0 0x0 0x0 0x0 0x7930007 0x12b 0x108 0x26e 0x7970005 0x26e 0x0 0x0 0x0 0x0 0x0 0x7a00007 0x1cf 0xb0 0x9f 0x7a80005 0x9f 0x0 0x0 0x0 0x0 0x0 0x7ad0007 0x0 0x58 0x9f 0x7b30005 0x9f 0x0 0x0 0x0 0x0 0x0 0x7bd0005 0x399 0x0 0x0 0x0 0x0 0x0 0x7c00003 0x399 0x8b0 0x7c80005 0x0 0x0 0x0 0x0 0x0 0x0 0x7d20005 0x0 0x0 0x0 0x0 0x0 0x0 0x7d50003 0x0 0x828 0x7e20005 0x8e 0x0 0x0 0x0 0x0 0x0 0x7e60005 0x8e 0x0 0x0 0x0 0x0 0x0 0x7e90003 0x8e 0x7a0 0x7ed0005 0x0 0x0 0x0 0x0 0x0 0x0 0x7f20008 0x12 0x0 0x320 0x0 0xa0 0x0 0xf0 0x0 0x230 0x0 0x280 0x0 0x140 0x0 0x190 0x0 0x1e0 0x0 0x2d0 0x8230005 0x0 0x0 0x0 0x0 0x0 0x0 0x8260003 0x0 0x678 0x82c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x82f0003 0x0 0x628 0x8350005 0x0 0x0 0x0 0x0 0x0 0x0 0x8380003 0x0 0x5d8 0x83e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8410003 0x0 0x588 0x8470005 0x0 0x0 0x0 0x0 0x0 0x0 0x84a0003 0x0 0x538 0x8500005 0x0 0x0 0x0 0x0 0x0 0x0 0x8530003 0x0 0x4e8 0x8590005 0x0 0x0 0x0 0x0 0x0 0x0 0x85c0003 0x0 0x498 0x8620005 0x0 0x0 0x0 0x0 0x0 0x0 0x8650003 0x0 0x448 0x86c0002 0x0 0x8770005 0x78 0x0 0x0 0x0 0x0 0x0 0x87e0005 0x78 0x0 0x0 0x0 0x0 0x0 0x8830007 0x78 0x128 0x0 0x88d0002 0x0 0x8920005 0x0 0x0 0x0 0x0 0x0 0x0 0x8970005 0x0 0x0 0x0 0x0 0x0 0x0 0x89a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x89d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8a00003 0x0 0x2a0 0x8aa0005 0x78 0x0 0x0 0x0 0x0 0x0 0x8ae0005 0x78 0x0 0x0 0x0 0x0 0x0 0x8b10003 0x78 0x218 0x8bb0005 0x183 0x0 0x0 0x0 0x0 0x0 0x8c20005 0x183 0x0 0x0 0x0 0x0 0x0 0x8c70007 0x17b 0x70 0x8 0x8cf0005 0x8 0x0 0x0 0x0 0x0 0x0 0x8d20003 0x8 0x138 0x8dc0005 0x17b 0x0 0x0 0x0 0x0 0x0 0x8e00005 0x17b 0x0 0x0 0x0 0x0 0x0 0x8e30003 0x17b 0xb0 0x8e80005 0x0 0x0 0x0 0x0 0x0 0x0 0x8f20005 0x0 0x0 0x0 0x0 0x0 0x0 0x8f50003 0x0 0x28 0x8fc0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x5 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame push (I)V 2 4315 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x40007 0xe44 0x20 0x1ca 0x1a0007 0x1003 0x40 0xb 0x260002 0xb 0x340002 0xb 0x610007 0x989 0x20 0x685 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x3c 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable addType (Ljava/lang/String;)I 2 2739 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 54 0x40002 0xa29 0xa0005 0xa29 0x0 0x0 0x0 0x0 0x0 0xf0007 0x27c 0xd0 0x86b 0x190007 0xbe 0x98 0x7ad 0x210007 0x0 0x78 0x7ad 0x290005 0x7ad 0x0 0x0 0x0 0x0 0x0 0x2c0007 0x0 0x20 0x7ad 0x390003 0xbe 0xffffffffffffff48 0x4a0002 0x27c 0x4d0005 0x27c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xfc 0xe oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;)V 2 1345 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 55 0x20005 0x501 0x0 0x0 0x0 0x0 0x0 0x70007 0x143 0x48 0x3be 0xb0002 0x3be 0xe0003 0x3be 0x18 0x160002 0x501 0x1d0007 0xc7 0xd0 0x43a 0x230005 0x43a 0x0 0x0 0x0 0x0 0x0 0x2a0007 0x14 0x40 0x426 0x310007 0x426 0x58 0x0 0x370005 0x14 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x3c 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame getLocal (I)I 2 1871 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0x40007 0x202 0x40 0x509 0xd0007 0x509 0x20 0x0 0x1d0007 0x335 0x20 0x1d4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop (I)V 2 1902 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x50007 0x5b 0x38 0x6b2 0x130003 0x6b2 0x18 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop ()I 2 1938 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x40007 0x7d 0x20 0x6b8 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame setLocal (II)V 1 789 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40007 0x1e6 0x20 0x110 0x170007 0x2d1 0x40 0x25 0x200002 0x25 0x300002 0x25 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x10 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 2 1168 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 58 0x20005 0x450 0x0 0x0 0x0 0x0 0x0 0x90007 0xa3 0x80 0x3ad 0xe0002 0x3ad 0x150005 0x3ad 0x0 0x0 0x0 0x0 0x0 0x180003 0x3ad 0xe0 0x1e0007 0x0 0x40 0xa3 0x240007 0xa3 0x70 0x0 0x290005 0x0 0x0 0x0 0x0 0x0 0x0 0x2c0003 0x0 0x50 0x310005 0xa3 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame addInitializedType (I)V 1 167 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40007 0x0 0x20 0x9f 0x190007 0x9f 0x40 0x0 0x250002 0x0 0x330002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x26 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable addUninitializedType (Ljava/lang/String;I)I 1 149 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 59 0x50002 0x8e 0xb0005 0x8e 0x0 0x0 0x0 0x0 0x0 0x120007 0x6c 0xf0 0x4a 0x1d0007 0x28 0xb8 0x22 0x260007 0x0 0x98 0x22 0x310007 0x0 0x78 0x22 0x3a0005 0x22 0x0 0x0 0x0 0x0 0x0 0x3d0007 0x0 0x20 0x22 0x4d0003 0x28 0xffffffffffffff28 0x600002 0x6c 0x630005 0x6c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xfc 0xe 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/SymbolTable addTypeInternal (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)I 1 799 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x40007 0x25d 0x20 0x4c 0x190007 0x298 0x30 0x11 0x330002 0x11 0x4b0004 0x0 0x0 0x2aa6ac1d570 0x2a9 0x0 0x0 0x4e0005 0x2a9 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xfc 0x40 oops 1 13 jdk/internal/org/objectweb/asm/SymbolTable$Entry methods 0
ciMethodData jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 2 2754 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 97 0x60005 0x628 0x0 0x0 0x0 0x0 0x0 0xd0007 0x628 0x1d8 0x8ca 0x130007 0x21 0x40 0x8a9 0x190007 0x8a9 0x38 0x0 0x220003 0x21 0x128 0x270005 0x978 0x0 0x0 0x0 0x0 0x0 0x2c0007 0x8a9 0x38 0xcf 0x320003 0xcf 0xffffffffffffffa8 0x3a0005 0x8a9 0x0 0x0 0x0 0x0 0x0 0x3f0007 0xdf 0x68 0x7ca 0x460005 0x7ca 0x0 0x0 0x0 0x0 0x0 0x500002 0x7ca 0x590005 0x8ca 0x0 0x0 0x0 0x0 0x0 0x5d0003 0x8ca 0xfffffffffffffe40 0x640005 0x628 0x0 0x0 0x0 0x0 0x0 0x6b0007 0x405 0x20 0x223 0x750007 0x18 0x40 0x3ed 0x7b0007 0x3ed 0x38 0x0 0x7f0003 0x18 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Frame getAbstractTypeFromDescriptor (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;I)I 2 1943 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 224 0x20005 0x690 0x0 0x0 0x0 0x0 0x0 0x50008 0x36 0x0 0x650 0x0 0x1c0 0x0 0x1c0 0x0 0x1c0 0x0 0x650 0x0 0x1c0 0x0 0x650 0x0 0x650 0x37 0x1c0 0xb 0x1c0 0x0 0x650 0x562 0x1c0 0x0 0x650 0x0 0x650 0x0 0x650 0x0 0x650 0x0 0x650 0x0 0x650 0x0 0x1c0 0x0 0x650 0x0 0x650 0xbf 0x1c0 0x0 0x650 0x0 0x650 0x0 0x650 0x26 0x1c0 0x7 0x268 0x8f0005 0x562 0x0 0x0 0x0 0x0 0x0 0x940005 0x562 0x0 0x0 0x0 0x0 0x0 0x9c0005 0x562 0x0 0x0 0x0 0x0 0x0 0xa90005 0x7 0x0 0x0 0x0 0x0 0x0 0xae0007 0x7 0x38 0x0 0xb40003 0x0 0xffffffffffffffa8 0xba0005 0x7 0x0 0x0 0x0 0x0 0x0 0xbd0008 0x34 0x0 0x330 0x0 0x1e0 0x0 0x1c8 0x0 0x258 0x0 0x330 0x0 0x228 0x0 0x330 0x0 0x330 0x2 0x210 0x0 0x240 0x0 0x330 0x5 0x270 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x1f8 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x330 0x0 0x1b0 0x1340003 0x0 0x190 0x13b0003 0x0 0x178 0x1420003 0x0 0x160 0x1490003 0x0 0x148 0x1500003 0x2 0x130 0x1570003 0x0 0x118 0x15e0003 0x0 0x100 0x1650003 0x0 0xe8 0x16e0005 0x5 0x0 0x0 0x0 0x0 0x0 0x1730005 0x5 0x0 0x0 0x0 0x0 0x0 0x17b0005 0x5 0x0 0x0 0x0 0x0 0x0 0x1810003 0x5 0x28 0x1880002 0x0 0x19b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/org/objectweb/asm/Type getReturnTypeOffset (Ljava/lang/String;)I 1 1736 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 57 0x40005 0x7ac 0x0 0x0 0x0 0x0 0x0 0x90007 0x2f7 0x148 0x4b5 0xe0005 0x551 0x0 0x0 0x0 0x0 0x0 0x130007 0x4b5 0x38 0x9c 0x190003 0x9c 0xffffffffffffffa8 0x210005 0x4b5 0x0 0x0 0x0 0x0 0x0 0x260007 0x5a 0xffffffffffffff00 0x45b 0x2d0005 0x45b 0x0 0x0 0x0 0x0 0x0 0x350002 0x45b 0x390003 0x45b 0xfffffffffffffe98 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
compile jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V -1 4 inline 151 0 -1 jdk/internal/org/objectweb/asm/Frame execute (IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V 1 1386 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 2235 jdk/internal/org/objectweb/asm/Frame pop ()I 1 2242 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 2268 jdk/internal/org/objectweb/asm/SymbolTable addType (Ljava/lang/String;)I 2 4 jdk/internal/org/objectweb/asm/SymbolTable hash (ILjava/lang/String;)I 3 4 java/lang/String hashCode ()I 4 17 java/lang/String isLatin1 ()Z 4 27 java/lang/StringLatin1 hashCode ([B)I 2 10 jdk/internal/org/objectweb/asm/SymbolTable get (I)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 2 41 java/lang/String equals (Ljava/lang/Object;)Z 2 74 jdk/internal/org/objectweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 3 7 jdk/internal/org/objectweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 4 1 java/lang/Object <init> ()V 2 77 jdk/internal/org/objectweb/asm/SymbolTable addTypeInternal (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)I 1 2272 jdk/internal/org/objectweb/asm/Frame push (I)V 1 2167 jdk/internal/org/objectweb/asm/Frame pop ()I 1 2174 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 2218 jdk/internal/org/objectweb/asm/SymbolTable addType (Ljava/lang/String;)I 2 4 jdk/internal/org/objectweb/asm/SymbolTable hash (ILjava/lang/String;)I 3 4 java/lang/String hashCode ()I 4 17 java/lang/String isLatin1 ()Z 4 27 java/lang/StringLatin1 hashCode ([B)I 2 10 jdk/internal/org/objectweb/asm/SymbolTable get (I)Ljdk/internal/org/objectweb/asm/SymbolTable$Entry; 2 41 java/lang/String equals (Ljava/lang/Object;)Z 2 74 jdk/internal/org/objectweb/asm/SymbolTable$Entry <init> (IILjava/lang/String;I)V 3 7 jdk/internal/org/objectweb/asm/Symbol <init> (IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V 4 1 java/lang/Object <init> ()V 2 77 jdk/internal/org/objectweb/asm/SymbolTable addTypeInternal (Ljdk/internal/org/objectweb/asm/SymbolTable$Entry;)I 1 2222 jdk/internal/org/objectweb/asm/Frame push (I)V 1 2022 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1932 jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 2 14 jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 3 6 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 39 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 58 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 70 java/lang/String indexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 indexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 3 89 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 100 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 2 21 jdk/internal/org/objectweb/asm/Frame pop (I)V 2 49 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 1943 jdk/internal/org/objectweb/asm/Frame pop ()I 1 1960 java/lang/String charAt (I)C 2 1 java/lang/String isLatin1 ()Z 2 12 java/lang/StringLatin1 charAt ([BI)C 1 1981 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 2 11 jdk/internal/org/objectweb/asm/Type getReturnTypeOffset (Ljava/lang/String;)I 3 4 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 14 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 33 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 45 java/lang/String indexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 indexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 2 35 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1895 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 1905 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 2 11 jdk/internal/org/objectweb/asm/Type getReturnTypeOffset (Ljava/lang/String;)I 3 4 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 14 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 33 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 45 java/lang/String indexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 indexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 2 35 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1887 jdk/internal/org/objectweb/asm/Frame pop (Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 2 14 jdk/internal/org/objectweb/asm/Type getArgumentsAndReturnSizes (Ljava/lang/String;)I 3 6 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 39 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 58 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 70 java/lang/String indexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 indexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 3 89 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 100 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 2 21 jdk/internal/org/objectweb/asm/Frame pop (I)V 2 49 jdk/internal/org/objectweb/asm/Frame pop (I)V 1 1876 jdk/internal/org/objectweb/asm/Frame push (Ljdk/internal/org/objectweb/asm/SymbolTable;Ljava/lang/String;)V 2 2 java/lang/String charAt (I)C 3 1 java/lang/String isLatin1 ()Z 3 12 java/lang/StringLatin1 charAt ([BI)C 2 11 jdk/internal/org/objectweb/asm/Type getReturnTypeOffset (Ljava/lang/String;)I 3 4 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 14 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 33 java/lang/String charAt (I)C 4 1 java/lang/String isLatin1 ()Z 4 12 java/lang/StringLatin1 charAt ([BI)C 3 45 java/lang/String indexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 indexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 2 35 jdk/internal/org/objectweb/asm/Frame push (I)V 1 1401 jdk/internal/org/objectweb/asm/Frame pop ()I 1 1409 jdk/internal/org/objectweb/asm/Frame push (I)V
