import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Share,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FastImage from 'react-native-fast-image';
import { RootStackParamList } from '../../navigation/AppNavigator';

const { width } = Dimensions.get('window');

type BlogDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'BlogDetail'>;
type BlogDetailScreenRouteProp = RouteProp<RootStackParamList, 'BlogDetail'>;

interface BlogPost {
  id: string;
  title: string;
  content: string;
  image: string;
  author: string;
  publishedAt: Date;
  category: string;
  readTime: number;
  tags: string[];
  likes: number;
  shares: number;
}

const BlogDetailScreen: React.FC = () => {
  const navigation = useNavigation<BlogDetailScreenNavigationProp>();
  const route = useRoute<BlogDetailScreenRouteProp>();
  const { postId } = route.params;
  
  const [post, setPost] = useState<BlogPost | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);

  // Mock data - replace with real API call
  const mockPost: BlogPost = {
    id: postId,
    title: 'Latest Fashion Trends 2024: What You Need to Know',
    content: `Fashion is constantly evolving, and 2024 brings exciting new trends that blend comfort with style. From sustainable materials to bold color palettes, this year's fashion landscape is diverse and inclusive.

## Sustainable Fashion Takes Center Stage

The fashion industry is embracing sustainability like never before. Brands are focusing on:

• Eco-friendly materials
• Circular fashion concepts  
• Reduced carbon footprint
• Ethical manufacturing processes

## Color Trends for 2024

This year's color palette is vibrant and optimistic:

**Bold Brights**: Electric blues, vibrant greens, and sunset oranges
**Soft Pastels**: Lavender, mint green, and peachy pink
**Classic Neutrals**: Warm beiges, soft grays, and cream tones

## Key Fashion Pieces

### Statement Outerwear
Oversized blazers and structured coats are making a comeback. These pieces offer versatility and can transform any outfit from casual to professional.

### Comfortable Footwear
Comfort meets style with chunky sneakers, platform sandals, and supportive loafers becoming wardrobe staples.

The key to mastering 2024's fashion trends is to stay true to your personal style while experimenting with new elements that excite you.`,
    image: 'https://via.placeholder.com/400x250',
    author: 'Sarah Johnson',
    publishedAt: new Date('2024-01-15'),
    category: 'Fashion',
    readTime: 5,
    tags: ['fashion', 'trends', '2024', 'style'],
    likes: 124,
    shares: 32,
  };

  useEffect(() => {
    loadBlogPost();
  }, [postId]);

  const loadBlogPost = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with real API call
      setPost(mockPost);
    } catch (error) {
      console.error('Failed to load blog post:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (!post) return;
    
    try {
      await Share.share({
        message: `Check out this article: ${post.title}`,
        url: `https://e-luxe.com/blog/${post.id}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    // TODO: Implement like functionality
  };

  const renderContent = (content: string) => {
    const lines = content.split('\n').filter(line => line.trim());
    
    return lines.map((line, index) => {
      if (line.startsWith('## ')) {
        return (
          <Text key={index} style={styles.heading}>
            {line.replace('## ', '')}
          </Text>
        );
      } else if (line.startsWith('### ')) {
        return (
          <Text key={index} style={styles.subheading}>
            {line.replace('### ', '')}
          </Text>
        );
      } else if (line.startsWith('**') && line.endsWith('**')) {
        return (
          <Text key={index} style={styles.boldText}>
            {line.replace(/\*\*/g, '')}
          </Text>
        );
      } else if (line.startsWith('• ')) {
        return (
          <Text key={index} style={styles.bulletPoint}>
            {line}
          </Text>
        );
      } else if (line.trim()) {
        return (
          <Text key={index} style={styles.paragraph}>
            {line}
          </Text>
        );
      }
      return null;
    });
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading article...</Text>
      </View>
    );
  }

  if (!post) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error-outline" size={64} color="#ccc" />
        <Text style={styles.errorTitle}>Article Not Found</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <FastImage source={{ uri: post.image }} style={styles.heroImage} />
        
        <View style={styles.articleHeader}>
          <View style={styles.metaInfo}>
            <Text style={styles.category}>{post.category}</Text>
            <Text style={styles.readTime}>{post.readTime} min read</Text>
          </View>
          
          <Text style={styles.title}>{post.title}</Text>
          
          <View style={styles.authorInfo}>
            <View style={styles.authorDetails}>
              <Text style={styles.authorName}>By {post.author}</Text>
              <Text style={styles.publishDate}>
                {post.publishedAt.toLocaleDateString()}
              </Text>
            </View>
            
            <View style={styles.socialActions}>
              <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
                <Icon 
                  name={isLiked ? "favorite" : "favorite-border"} 
                  size={20} 
                  color={isLiked ? "#FF3B30" : "#666"} 
                />
                <Text style={styles.actionText}>{post.likes + (isLiked ? 1 : 0)}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
                <Icon name="share" size={20} color="#666" />
                <Text style={styles.actionText}>{post.shares}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        
        <View style={styles.contentContainer}>
          {renderContent(post.content)}
        </View>
        
        <View style={styles.tagsContainer}>
          <Text style={styles.tagsTitle}>Tags:</Text>
          <View style={styles.tagsWrapper}>
            {post.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>#{tag}</Text>
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    backgroundColor: '#F8F9FA',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  heroImage: {
    width: width,
    height: 250,
  },
  articleHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  category: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    textTransform: 'uppercase',
  },
  readTime: {
    fontSize: 12,
    color: '#666',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
    lineHeight: 32,
    marginBottom: 16,
  },
  authorInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  publishDate: {
    fontSize: 12,
    color: '#666',
  },
  socialActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  },
  actionText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  contentContainer: {
    padding: 20,
  },
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
    marginTop: 24,
    marginBottom: 12,
    lineHeight: 28,
  },
  subheading: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    lineHeight: 24,
  },
  paragraph: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 16,
  },
  boldText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 4,
    paddingLeft: 8,
  },
  tagsContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  tagsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  bottomSpacing: {
    height: 40,
  },
});

export default BlogDetailScreen;
