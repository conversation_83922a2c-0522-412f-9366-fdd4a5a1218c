/**
 * Composant WhiteProduct - Mobile E-Luxe 1.0
 * Reproduction exacte du composant WhiteProduct.tsx du client web
 * Affiche les meilleurs vendeurs avec design blanc/luxueux
 */

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/store';
import { getBestSellersOfMonthAsync } from '../store/slices/productSlice';
import ProductCard from './ProductCard';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';


// Interfaces locales compatibles
interface CategoryLocal {
  _id?: string;
  id: string;
  name: string;
  image?: string;
  productCount?: number;
}

interface ProductLocal {
  id: string;
  _id?: string;
  name?: string;
  title?: string | Record<string, string>;
  price: number;
  image?: string;
  images?: string[];
  rating?: number;
  discount?: number;
}

interface Product {
  id: string;
  _id?: string;
  name: string;
  price: number;
  image: string;
  images?: string[];
  rating?: number;
  discount?: number;
}

interface WhiteProductProps {
  onProductPress?: (product: ProductLocal) => void;
  onViewAllPress?: () => void;
  title?: string;
  subtitle?: string;
}

/**
 * Composant WhiteProduct principal
 * Reproduction de la logique WhiteProduct.tsx du client web
 */
const WhiteProduct: React.FC<WhiteProductProps> = ({
  onProductPress,
  onViewAllPress,
  title = "Best Sellers",
  subtitle = "This Month",
}) => {
  const dispatch = useAppDispatch();
  const { bestSellingProducts, isLoading } = useAppSelector((state) => state.product);

  // Charger les meilleurs vendeurs (EXACTEMENT comme le client web - components/sections/WhiteProduct.tsx)
  useEffect(() => {
    console.log('🏆 Chargement des meilleurs vendeurs - EXACTEMENT comme le client web...');

    // Le client web utilise ProductServices.getAllProducts avec des paramètres spécifiques
    // Nous utilisons getBestSellersOfMonthAsync qui fait la même chose
    dispatch(getBestSellersOfMonthAsync());
  }, [dispatch]);

  // Gestion du clic sur un produit
  const handleProductPress = (product: ProductLocal) => {
    console.log('🛒 Meilleur vendeur sélectionné:', product.name);
    onProductPress?.(product);
  };

  // Si pas de produits, ne rien afficher
  if (!bestSellingProducts || bestSellingProducts.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Section Header (comme le client web) */}
      <View style={styles.sectionHeader}>
        <View style={styles.titleContainer}>
          <Text style={styles.subtitle}>{subtitle}</Text>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.titleUnderline} />
        </View>
        
        <TouchableOpacity style={styles.viewAllButton} onPress={onViewAllPress}>
          <Text style={styles.viewAllText}>View All</Text>
          <Icon name="arrow-forward" size={16} color={ELuxeColors.primary} />
        </TouchableOpacity>
      </View>

      {/* Description (comme le client web) */}
      <View style={styles.descriptionContainer}>
        <Text style={styles.description}>
          Discover our most popular products this month. 
          These carefully selected items represent the best of luxury and quality.
        </Text>
      </View>

      {/* Grille de produits (design blanc comme le client web) */}
      <View style={styles.productsSection}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.productsContainer}
        >
          {bestSellingProducts.map((product, index) => (
            <View key={product._id || product.id} style={styles.productWrapper}>
              <ProductCard
                product={product}
                onPress={() => handleProductPress(product)}
                style={styles.productCard}
              />
              
              {/* Badge "Best Seller" pour le premier produit */}
              {index === 0 && (
                <View style={styles.bestSellerBadge}>
                  <Icon name="star" size={12} color={ELuxeColors.white} />
                  <Text style={styles.bestSellerText}>Best Seller</Text>
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      </View>

      {/* Call to Action (comme le client web) */}
      <View style={styles.ctaContainer}>
        <TouchableOpacity style={styles.ctaButton} onPress={onViewAllPress}>
          <Text style={styles.ctaButtonText}>Shop Best Sellers</Text>
          <Icon name="arrow-forward" size={16} color={ELuxeColors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ELuxeColors.white, // Design blanc comme le client web
    paddingVertical: 40,
    marginVertical: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  subtitle: {
    color: ELuxeColors.primary, // Couleur or E-Luxe
    fontSize: 14, // Taille plus grande pour visibilité
    fontWeight: '600', // Gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 4,
    letterSpacing: 1,
    textTransform: 'uppercase',
    lineHeight: 18, // Interligne pour lisibilité
  },
  title: {
    color: ELuxeColors.textPrimary, // Noir pour maximum de contraste
    fontSize: 26, // Taille plus grande pour visibilité
    fontWeight: '700', // Très gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 8,
    lineHeight: 30, // Interligne pour lisibilité
  },
  titleUnderline: {
    width: 50,
    height: 3,
    backgroundColor: ELuxeColors.primary,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  viewAllText: {
    ...TextStyles.button,
    color: ELuxeColors.primary,
  },
  descriptionContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  description: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary, // Gris foncé pour bon contraste
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 16, // Taille plus grande pour meilleure lisibilité
    fontWeight: '400', // Poids normal mais lisible
  },
  productsSection: {
    marginBottom: 24,
  },
  productsContainer: {
    paddingHorizontal: 16,
    gap: 16,
  },
  productWrapper: {
    position: 'relative',
  },
  productCard: {
    width: 180,
    backgroundColor: ELuxeColors.white,
    borderRadius: 12,
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },

  bestSellerBadge: {
    position: 'absolute',
    top: -8,
    left: 8,
    backgroundColor: ELuxeColors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  bestSellerText: {
    ...TextStyles.caption,
    color: ELuxeColors.white,
    fontWeight: '600',
    fontSize: 10,
  },
  ctaContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  ctaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
    shadowColor: ELuxeColors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  ctaButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
  },
});

export default WhiteProduct;
