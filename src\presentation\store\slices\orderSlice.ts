import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Order, ShippingAddress, OrderTracking } from '../../../domain/entities/Order';
import { ecommerceService } from '../../../services/EcommerceApiService';

// State interface
interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  shippingAddresses: ShippingAddress[];
  orderTracking: OrderTracking | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    totalPages: number;
    total: number;
  };
}

// Initial state
const initialState: OrderState = {
  orders: [],
  currentOrder: null,
  shippingAddresses: [],
  orderTracking: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    totalPages: 1,
    total: 0,
  },
};

// Async thunks (placeholder implementations)
export const getOrdersAsync = createAsyncThunk(
  'order/getOrders',
  async (page: number = 1, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return {
        orders: [],
        total: 0,
        page: 1,
        totalPages: 1,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch orders');
    }
  }
);

export const getOrderByIdAsync = createAsyncThunk(
  'order/getOrderById',
  async (orderId: string, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return null as Order | null;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch order');
    }
  }
);

export const createOrderAsync = createAsyncThunk(
  'order/createOrder',
  async (orderData: Omit<Order, 'id' | 'order_number' | 'created_at' | 'updated_at'>, { rejectWithValue }) => {
    try {
      console.log('🛒 Création de commande:', orderData);

      // Appel API réel pour créer la commande - EXACTEMENT comme le client web
      const newOrder = await ecommerceService.createOrder({
        items: orderData.cart?.items || [],
        shippingAddress: orderData.shipping_address,
        billingAddress: orderData.billing_address,
        paymentMethod: orderData.payment_method || 'card'
      });
      console.log('✅ Commande créée:', newOrder);

      return newOrder;
    } catch (error) {
      console.error('❌ Erreur création commande:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create order');
    }
  }
);

export const getShippingAddressesAsync = createAsyncThunk(
  'order/getShippingAddresses',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return [] as ShippingAddress[];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch shipping addresses');
    }
  }
);

export const addShippingAddressAsync = createAsyncThunk(
  'order/addShippingAddress',
  async (address: Omit<ShippingAddress, 'id' | 'userId' | 'created_at' | 'updated_at'>, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      const newAddress: ShippingAddress = {
        ...address,
        id: Date.now().toString(),
        userId: 'current-user-id', // Should come from auth state
        created_at: new Date(),
        updated_at: new Date(),
      };
      return newAddress;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to add shipping address');
    }
  }
);

export const updateShippingAddressAsync = createAsyncThunk(
  'order/updateShippingAddress',
  async ({ addressId, address }: { addressId: string; address: Partial<ShippingAddress> }, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return { addressId, address };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update shipping address');
    }
  }
);

export const deleteShippingAddressAsync = createAsyncThunk(
  'order/deleteShippingAddress',
  async (addressId: string, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return addressId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete shipping address');
    }
  }
);

export const getOrderTrackingAsync = createAsyncThunk(
  'order/getOrderTracking',
  async (orderId: string, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual repository
      return null as OrderTracking | null;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch order tracking');
    }
  }
);

// Slice
const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentOrder: (state, action: PayloadAction<Order | null>) => {
      state.currentOrder = action.payload;
    },
    clearOrders: (state) => {
      state.orders = [];
      state.pagination = {
        page: 1,
        totalPages: 1,
        total: 0,
      };
    },
  },
  extraReducers: (builder) => {
    // Get orders
    builder
      .addCase(getOrdersAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getOrdersAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.orders;
        state.pagination = {
          page: action.payload.page,
          totalPages: action.payload.totalPages,
          total: action.payload.total,
        };
        state.error = null;
      })
      .addCase(getOrdersAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Get order by ID
    builder
      .addCase(getOrderByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getOrderByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentOrder = action.payload;
        state.error = null;
      })
      .addCase(getOrderByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create order
    builder
      .addCase(createOrderAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createOrderAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload.data || action.payload;
        state.currentOrder = data;
        state.orders.unshift(data);
        state.error = null;
      })
      .addCase(createOrderAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Get shipping addresses
    builder
      .addCase(getShippingAddressesAsync.fulfilled, (state, action) => {
        state.shippingAddresses = action.payload;
      })
      .addCase(getShippingAddressesAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Add shipping address
    builder
      .addCase(addShippingAddressAsync.fulfilled, (state, action) => {
        state.shippingAddresses.push(action.payload);
      })
      .addCase(addShippingAddressAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Update shipping address
    builder
      .addCase(updateShippingAddressAsync.fulfilled, (state, action) => {
        const index = state.shippingAddresses.findIndex(addr => addr.id === action.payload.addressId);
        if (index !== -1) {
          state.shippingAddresses[index] = {
            ...state.shippingAddresses[index],
            ...action.payload.address,
            updated_at: new Date(),
          };
        }
      })
      .addCase(updateShippingAddressAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Delete shipping address
    builder
      .addCase(deleteShippingAddressAsync.fulfilled, (state, action) => {
        state.shippingAddresses = state.shippingAddresses.filter(addr => addr.id !== action.payload);
      })
      .addCase(deleteShippingAddressAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Get order tracking
    builder
      .addCase(getOrderTrackingAsync.fulfilled, (state, action) => {
        state.orderTracking = action.payload;
      })
      .addCase(getOrderTrackingAsync.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentOrder, clearOrders } = orderSlice.actions;
export default orderSlice.reducer;
