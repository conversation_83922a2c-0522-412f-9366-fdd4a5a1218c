#!/usr/bin/env node

/**
 * Script de vérification TypeScript personnalisé pour E-Luxe Mobile
 * Ce script vérifie uniquement les fichiers source de l'application
 * et ignore les erreurs dans node_modules
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification TypeScript des fichiers source...\n');

try {
  // Créer un tsconfig temporaire qui exclut node_modules
  const tempTsConfig = {
    "extends": "@tsconfig/react-native/tsconfig.json",
    "compilerOptions": {
      "target": "es2020",
      "lib": ["es2020", "es6", "dom"],
      "allowJs": true,
      "skipLibCheck": true,
      "skipDefaultLibCheck": true,
      "esModuleInterop": true,
      "allowSyntheticDefaultImports": true,
      "strict": false,
      "forceConsistentCasingInFileNames": true,
      "moduleResolution": "node",
      "resolveJsonModule": true,
      "isolatedModules": true,
      "noEmit": true,
      "jsx": "react-jsx",
      "noImplicitAny": false,
      "noImplicitReturns": false,
      "noImplicitThis": false,
      "noUnusedLocals": false,
      "noUnusedParameters": false,
      "baseUrl": "./src",
      "paths": {
        "@/*": ["*"],
        "@/components/*": ["presentation/components/*"],
        "@/screens/*": ["presentation/screens/*"],
        "@/navigation/*": ["presentation/navigation/*"],
        "@/hooks/*": ["presentation/hooks/*"],
        "@/store/*": ["presentation/store/*"],
        "@/domain/*": ["domain/*"],
        "@/data/*": ["data/*"],
        "@/utils/*": ["utils/*"],
        "@/types/*": ["types/*"],
        "@/constants/*": ["constants/*"]
      }
    },
    "include": [
      "src/**/*",
      "App.tsx",
      "index.js"
    ],
    "exclude": [
      "node_modules/**/*",
      "android/**/*",
      "ios/**/*",
      "**/*.test.ts",
      "**/*.test.tsx",
      "**/*.spec.ts",
      "**/*.spec.tsx"
    ]
  };

  // Écrire le fichier temporaire
  const tempConfigPath = path.join(process.cwd(), 'tsconfig.temp.json');
  fs.writeFileSync(tempConfigPath, JSON.stringify(tempTsConfig, null, 2));

  // Exécuter TypeScript avec le fichier temporaire
  console.log('📝 Compilation TypeScript en cours...');
  execSync(`npx tsc --noEmit --project ${tempConfigPath}`, { 
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // Nettoyer le fichier temporaire
  fs.unlinkSync(tempConfigPath);

  console.log('\n✅ Vérification TypeScript terminée avec succès !');
  console.log('🎉 Aucune erreur TypeScript détectée dans les fichiers source.');

} catch (error) {
  // Nettoyer le fichier temporaire en cas d'erreur
  const tempConfigPath = path.join(process.cwd(), 'tsconfig.temp.json');
  if (fs.existsSync(tempConfigPath)) {
    fs.unlinkSync(tempConfigPath);
  }

  console.log('\n❌ Erreurs TypeScript détectées :');
  console.log(error.stdout?.toString() || error.message);
  
  console.log('\n💡 Conseils pour corriger les erreurs :');
  console.log('1. Vérifiez les imports et exports');
  console.log('2. Assurez-vous que tous les types sont correctement définis');
  console.log('3. Vérifiez la syntaxe TypeScript');
  
  process.exit(1);
}
