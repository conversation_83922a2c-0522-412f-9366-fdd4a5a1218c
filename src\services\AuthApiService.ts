/**
 * Service API d'Authentification pour Mobile E-Luxe 1.0
 * Compatible avec l'API v1/{lang}/auth/login
 */

import { apiClient } from '../data/datasources/ApiClient';
import { API_ENDPOINTS } from '../config/ApiConfig';

// Types pour l'authentification
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: User;
  refreshToken?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  confirmPassword?: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  user: User;
  token?: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role?: string;
  avatar?: string;
  phone?: string;
  emailVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Service API d'Authentification
 * Utilise l'API v1/{lang}/auth/* avec support automatique de la langue
 */
export class AuthApiService {
  
  /**
   * Connexion utilisateur
   * POST /v1/{lang}/auth/login
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      console.log('🔐 Tentative de connexion:', credentials.email);
      const response = await apiClient.postWithLang<LoginResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      );
      console.log('✅ Réponse de connexion:', response);
      return response.data ? response.data : response;
    } catch (error: any) {
      console.error('❌ AuthApiService - Erreur de connexion:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      // Gestion des erreurs spécifiques selon le format E-Luxe
      if (error.response) {
        const status = error.response.status;
        const responseData = error.response.data;

        // Extraire le message d'erreur selon le format E-Luxe
        let errorMessage = 'Erreur de connexion';

        if (responseData?.data?.errMsg) {
          // Format E-Luxe: { status: "FAILED", data: { errNo: 7, errMsg: "message" } }
          errorMessage = responseData.data.errMsg;
        } else if (responseData?.message) {
          // Format standard
          errorMessage = responseData.message;
        } else if (responseData?.errMsg) {
          // Format alternatif
          errorMessage = responseData.errMsg;
        }

        switch (status) {
          case 400:
            throw new Error(`Données invalides: ${errorMessage}`);
          case 401:
            throw new Error(`Email ou mot de passe incorrect: ${errorMessage}`);
          case 403:
            throw new Error(`Compte désactivé ou non vérifié: ${errorMessage}`);
          case 404:
            throw new Error(`Service d'authentification non disponible: ${errorMessage}`);
          case 429:
            throw new Error(`Trop de tentatives. Veuillez réessayer plus tard: ${errorMessage}`);
          case 500:
            throw new Error(`Erreur serveur. Veuillez réessayer: ${errorMessage}`);
          default:
            throw new Error(`Erreur ${status}: ${errorMessage}`);
        }
      } else if (error.request) {
        throw new Error('Impossible de contacter le serveur. Vérifiez votre connexion internet');
      } else {
        throw new Error('Erreur de connexion: ' + (error.message || 'Erreur inconnue'));
      }
    }
  }

  /**
   * Inscription utilisateur
   * POST /v1/{lang}/auth/register
   */
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    try {
      const response = await apiClient.postWithLang<RegisterResponse>(
        API_ENDPOINTS.AUTH.REGISTER,
        userData
      );
      return response.data ? response.data : { user: response, token: '' };
    } catch (error) {
      console.error('Register error:', error);
      throw new Error('Registration failed: ' + (error as Error).message);
    }
  }

  /**
   * Récupération du profil utilisateur
   * GET /v1/{lang}/auth/me
   */
  async getProfile(): Promise<User> {
    try {
      const response = await apiClient.getWithLang<User>(
        API_ENDPOINTS.AUTH.ME
      );
      return response.data ? response.data : response;
    } catch (error) {
      console.error('Get profile error:', error);
      throw new Error('Failed to get profile: ' + (error as Error).message);
    }
  }

  /**
   * Mise à jour du profil utilisateur
   * PUT /v1/{lang}/auth/profile
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.putWithLang<User>(
        API_ENDPOINTS.AUTH.PROFILE,
        userData
      );
      return response.data ? response.data : response;
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error('Failed to update profile: ' + (error as Error).message);
    }
  }

  /**
   * Déconnexion utilisateur
   * POST /v1/{lang}/auth/logout
   */
  async logout(): Promise<void> {
    try {
      await apiClient.postWithLang<void>(
        API_ENDPOINTS.AUTH.LOGOUT
      );
    } catch (error) {
      console.error('Logout error:', error);
      // Ne pas throw d'erreur pour logout, juste logger
    }
  }

  /**
   * Refresh du token
   * POST /v1/{lang}/auth/refresh
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      const response = await apiClient.postWithLang<LoginResponse>(
        API_ENDPOINTS.AUTH.REFRESH,
        { refreshToken }
      );
      return response.data ? response.data : response;
    } catch (error) {
      console.error('Refresh token error:', error);
      throw new Error('Failed to refresh token: ' + (error as Error).message);
    }
  }

  /**
   * Mot de passe oublié
   * POST /v1/{lang}/auth/forgot-password
   */
  async forgotPassword(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.postWithLang<{ success: boolean; message: string }>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        { email }
      );
      return response;
    } catch (error) {
      console.error('Forgot password error:', error);
      throw new Error('Failed to send reset email: ' + (error as Error).message);
    }
  }

  /**
   * Réinitialisation du mot de passe
   * POST /v1/{lang}/auth/reset-password
   */
  async resetPassword(data: ResetPasswordRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.postWithLang<{ success: boolean; message: string }>(
        API_ENDPOINTS.AUTH.RESET_PASSWORD,
        data
      );
      return response;
    } catch (error) {
      console.error('Reset password error:', error);
      throw new Error('Failed to reset password: ' + (error as Error).message);
    }
  }

  /**
   * Changement de mot de passe
   * POST /v1/{lang}/auth/change-password
   */
  async changePassword(data: ChangePasswordRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.postWithLang<{ success: boolean; message: string }>(
        API_ENDPOINTS.AUTH.CHANGE_PASSWORD,
        data
      );
      return response;
    } catch (error) {
      console.error('Change password error:', error);
      throw new Error('Failed to change password: ' + (error as Error).message);
    }
  }

  /**
   * Validation du token
   * GET /v1/{lang}/auth/validate
   */
  async validateToken(): Promise<{ valid: boolean; user?: User }> {
    try {
      const response = await apiClient.getWithLang<{ valid: boolean; user?: User }>(
        API_ENDPOINTS.AUTH.VALIDATE
      );
      return response.data ? response.data : { valid: true, user: response };
    } catch (error) {
      console.error('Validate token error:', error);
      return { valid: false };
    }
  }
}

// Instance singleton
export const authApi = new AuthApiService();
