/**
 * Composant TopCategories - Mobile E-Luxe 1.0
 * Reproduction exacte du composant Category.tsx du client web
 * Affiche les catégories en vedette avec compteur de produits
 */

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/store';
import { getShowingCategoriesAsync } from '../store/slices/productSlice';
import { ELuxeColors, ComponentColors } from '../../theme/colors';
import { TextStyles } from '../../theme/typography';
import { ELuxeIcon } from '../../components/common/Icon';
import { getCategoryName, getCategoryImage } from '../../utils/translationUtils';


// Interfaces locales compatibles
interface CategoryLocal {
  _id?: string;
  id: string;
  name: string;
  image?: string;
  productCount?: number;
  product_count?: number; // Comme l'API backend
}

interface ProductLocal {
  id: string;
  _id?: string;
  name?: string;
  title?: string | Record<string, string>;
  price: number;
  image?: string;
  images?: string[];
  rating?: number;
  discount?: number;
}

interface Category {
  _id: string;
  id: string;
  name: string;
  image: string;
  productCount?: number;
}

interface TopCategoriesProps {
  onCategoryPress?: (category: CategoryLocal) => void;
  title?: string;
  showTitle?: boolean;
}

/**
 * Composant CategoryItem - Reproduction du style du client web
 */
const CategoryItem: React.FC<{
  category: CategoryLocal;
  onPress: () => void;
}> = ({ category, onPress }) => {
  
  // Fonction pour formater le nombre (comme padNumber du client web)
  const formatProductCount = (count: number = 0): string => {
    return count.toString().padStart(2, '0');
  };

  return (
    <TouchableOpacity style={styles.categoryItem} onPress={onPress}>
      <View style={styles.categoryIcon}>
        {/* Gestion d'image EXACTEMENT comme le client web (CategoryTable.tsx) */}
        <Image
          source={{ uri: getCategoryImage(category) }}
          style={styles.categoryImage}
          resizeMode="contain"
          onError={() => {
            console.log('⚠️ Erreur chargement image catégorie:', getCategoryName(category));
          }}
        />
        <View style={styles.productCountBadge}>
          <Text style={styles.productCountText}>
            {formatProductCount(category.productCount || category.product_count || 0)}
          </Text>
        </View>
      </View>
      <View style={styles.categoryContent}>
        <Text style={styles.categoryTitle} numberOfLines={2}>
          {getCategoryName(category)}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

/**
 * Composant TopCategories principal
 * Reproduction de la logique Category.tsx du client web
 */
const TopCategories: React.FC<TopCategoriesProps> = ({
  onCategoryPress,
  title = "Top Categories",
  showTitle = true,
}) => {
  const dispatch = useAppDispatch();
  const { showingCategories, isLoading } = useAppSelector((state) => state.product);

  // Charger les catégories showing (comme le client web)
  useEffect(() => {
    dispatch(getShowingCategoriesAsync());
  }, [dispatch]);

  // Gestion du clic sur une catégorie
  const handleCategoryPress = (category: CategoryLocal) => {
    const categoryName = getCategoryName(category);
    console.log('📂 Catégorie sélectionnée:', categoryName);
    onCategoryPress?.(category);
  };

  // Si pas de catégories, ne rien afficher
  if (!showingCategories || showingCategories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Titre de la section (comme le client web) */}
      {showTitle && (
        <View style={styles.sectionHeader}>
          <View style={styles.titleContainer}>
            <Text style={styles.sectionTitle}>{title}</Text>
            <View style={styles.titleUnderline} />
          </View>
          <TouchableOpacity style={styles.viewAllButton}>
            <Text style={styles.viewAllText}>View All</Text>
            <ELuxeIcon icon="arrowRight" size="sm" color={ELuxeColors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Grille de catégories (comme custom-row du client web) */}
      <View style={styles.categoriesGrid}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {showingCategories.map((category) => (
            <CategoryItem
              key={category._id || category.id}
              category={category}
              onPress={() => handleCategoryPress(category)}
            />
          ))}
        </ScrollView>
      </View>

      {/* Bordure inférieure (comme category-border du client web) */}
      <View style={styles.sectionBorder} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    backgroundColor: ComponentColors.screen.backgroundAlt,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  titleContainer: {
    alignItems: 'flex-start',
  },
  sectionTitle: {
    color: ELuxeColors.textPrimary, // Noir pour maximum de contraste
    fontSize: 22, // Taille plus grande pour visibilité
    fontWeight: '700', // Très gras pour visibilité
    fontFamily: 'Roboto', // Police Android native
    marginBottom: 4,
    lineHeight: 26, // Interligne pour lisibilité
  },
  titleUnderline: {
    width: 40,
    height: 2,
    backgroundColor: ELuxeColors.primary,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewAllText: {
    ...TextStyles.button,
    color: ELuxeColors.primary,
  },
  categoriesGrid: {
    paddingHorizontal: 16,
  },
  categoriesContainer: {
    gap: 16,
    paddingRight: 16,
  },
  categoryItem: {
    alignItems: 'center',
    width: 100,
  },
  categoryIcon: {
    position: 'relative',
    width: 80,
    height: 80,
    backgroundColor: ELuxeColors.white, // Fond blanc pour meilleure visibilité
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 2,
    borderColor: ELuxeColors.primary, // Bordure or E-Luxe pour plus de style
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryImage: {
    width: 50,
    height: 50,
  },
  productCountBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: ELuxeColors.primary, // Couleur or E-Luxe
    borderRadius: 15, // Plus grand pour meilleure visibilité
    minWidth: 30, // Plus large
    height: 30, // Plus haut
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3, // Bordure plus épaisse
    borderColor: ELuxeColors.white,
    shadowColor: ELuxeColors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  productCountText: {
    ...TextStyles.caption,
    color: ELuxeColors.black, // Noir sur fond or pour contraste maximal
    fontSize: 12, // Plus grand pour meilleure lisibilité
    fontWeight: '800', // Très gras
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  categoryContent: {
    alignItems: 'center',
  },
  categoryTitle: {
    ...TextStyles.bodySmall,
    color: ELuxeColors.textPrimary, // Noir pour maximum de contraste
    textAlign: 'center',
    fontWeight: '600', // Plus gras pour meilleure visibilité
    fontSize: 12, // Taille appropriée
    lineHeight: 16, // Interligne pour lisibilité
  },
  sectionBorder: {
    height: 1,
    backgroundColor: ELuxeColors.border1,
    marginTop: 20,
    marginHorizontal: 16,
  },
});

export default TopCategories;
