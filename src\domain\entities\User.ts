export interface User {
  id: string;
  username?: string;
  email: string;
  phone?: string;
  address?: string;
  gender?: string;
  avatar?: string;
  cover?: string;
  currency?: string;
  country?: string;
  city?: string;
  status: 'Active' | 'Inactive';
  online: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UserProfile extends User {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language: string;
  currency: string;
  notifications: NotificationSettings;
  theme: 'light' | 'dark' | 'auto';
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  newsletter: boolean;
}

export interface AuthUser {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  username?: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  acceptTerms: boolean;
}

export interface ResetPasswordData {
  email: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
