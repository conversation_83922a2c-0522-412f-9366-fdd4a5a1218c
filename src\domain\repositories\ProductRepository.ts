import { Product, Category, ProductFilter, ProductReview, ProductWishlist } from '../entities/Product';

export interface ProductRepository {
  // Product Management
  getProducts(filter?: ProductFilter): Promise<{
    products: Product[];
    total: number;
    page: number;
    totalPages: number;
  }>;
  getProductById(id: string): Promise<Product>;
  getProductBySlug(slug: string): Promise<Product>;
  searchProducts(query: string, filter?: ProductFilter): Promise<Product[]>;
  
  // Categories
  getCategories(): Promise<Category[]>;
  getCategoryById(id: string): Promise<Category>;
  getProductsByCategory(categoryId: string, filter?: ProductFilter): Promise<Product[]>;
  
  // Product Relationships
  getRelatedProducts(productId: string): Promise<Product[]>;
  getUpsellProducts(productId: string): Promise<Product[]>;
  getCrossSellProducts(productId: string): Promise<Product[]>;
  
  // Featured Products
  getFeaturedProducts(limit?: number): Promise<Product[]>;
  getBestSellingProducts(limit?: number): Promise<Product[]>;
  getNewArrivals(limit?: number): Promise<Product[]>;
  getDealsOfTheDay(limit?: number): Promise<Product[]>;
  
  // Reviews
  getProductReviews(productId: string, page?: number): Promise<{
    reviews: ProductReview[];
    total: number;
    averageRating: number;
    ratingDistribution: Record<number, number>;
  }>;
  addProductReview(productId: string, review: Omit<ProductReview, 'id' | 'created_at'>): Promise<ProductReview>;
  updateProductReview(reviewId: string, review: Partial<ProductReview>): Promise<ProductReview>;
  deleteProductReview(reviewId: string): Promise<void>;
  markReviewHelpful(reviewId: string): Promise<void>;
  
  // Wishlist
  getWishlist(userId: string): Promise<ProductWishlist[]>;
  addToWishlist(userId: string, productId: string): Promise<ProductWishlist>;
  removeFromWishlist(userId: string, productId: string): Promise<void>;
  isInWishlist(userId: string, productId: string): Promise<boolean>;
  
  // Recently Viewed
  getRecentlyViewed(userId: string): Promise<Product[]>;
  addToRecentlyViewed(userId: string, productId: string): Promise<void>;
  clearRecentlyViewed(userId: string): Promise<void>;
}

export interface ProductState {
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  currentProduct: Product | null;
  wishlist: ProductWishlist[];
  recentlyViewed: Product[];
  isLoading: boolean;
  error: string | null;
  filter: ProductFilter;
  pagination: {
    page: number;
    totalPages: number;
    total: number;
  };
}
