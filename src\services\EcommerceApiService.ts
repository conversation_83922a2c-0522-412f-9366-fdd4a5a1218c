/**
 * Service API E-commerce pour Mobile E-Luxe 1.0
 * Compatible avec l'architecture microservices existante
 */

import { apiClient } from '../data/datasources/ApiClient';
import { API_ENDPOINTS, buildUrl } from '../config/ApiConfig';

// Types pour les produits (compatibles avec l'app web)
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  images: string[];
  category: Category;
  brand?: string;
  stock: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  variants?: ProductVariant[];
  specifications?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  image?: string;
  parentId?: string;
  children?: Category[];
}

export interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  stock?: number;
}

// Types pour les commandes
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  total: number;
  subtotal: number;
  tax: number;
  shipping: number;
  status: OrderStatus;
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
  total: number;
  variant?: ProductVariant;
}

export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

export interface Address {
  id?: string;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
  isDefault?: boolean;
}

// Types pour le panier
export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  variant?: ProductVariant;
  addedAt: string;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  subtotal: number;
  total: number;
  itemCount: number;
  updatedAt: string;
}

/**
 * Service API E-commerce
 * Utilise la même architecture que l'application web client-e-luxe
 */
export class EcommerceApiService {
  
  // ===== PRODUITS =====
  
  /**
   * Récupère la liste des produits avec pagination et filtres - EXACTEMENT comme le client web
   */
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: 'name' | 'price' | 'rating' | 'created';
    sortOrder?: 'asc' | 'desc';
  }) {
    console.log('📦 EcommerceApiService.getProducts - params:', params);

    try {
      const response = await apiClient.getWithLang<{ products: Product[]; total: number; page: number; totalPages: number }>(
        '/v1/{lang}/products',
        undefined,
        { params }
      );

      console.log('✅ Produits récupérés:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des produits:', error);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un produit
   */
  async getProduct(id: string) {
    return apiClient.getWithLang<Product>(API_ENDPOINTS.PRODUCTS.DETAIL, { id });
  }

  /**
   * Récupère les produits en vedette
   */
  async getFeaturedProducts() {
    console.log('⭐ EcommerceApiService.getFeaturedProducts');
    // Utilise /products/showing car /products/featured n'existe pas dans l'API Gateway
    return apiClient.getWithLang<Product[]>(API_ENDPOINTS.PRODUCTS.SHOWING);
  }

  /**
   * Récupère les produits affichés (showing) - comme le client web
   */
  async getShowingProducts() {
    console.log('👁️ EcommerceApiService.getShowingProducts');
    return apiClient.getWithLang<Product[]>(API_ENDPOINTS.PRODUCTS.SHOWING);
  }

  /**
   * Récupère les meilleurs vendeurs du mois - comme le client web
   */
  async getBestSellersOfMonth() {
    console.log('🏆 EcommerceApiService.getBestSellersOfMonth');
    // Utilise l'endpoint EXACT du client web avec tiret (comme dans ProductServices.getBestSellersOfMonth)
    return apiClient.getWithLang<Product[]>('/v1/{lang}/products/best-sellers');
  }

  /**
   * Récupère un produit par son ID - EXACTEMENT comme le client web
   */
  async getProductById(productId: string): Promise<any> {
    try {
      console.log('🔍 EcommerceApiService.getProductById - ID:', productId);

      // Utiliser l'endpoint correct selon l'API Gateway : /v1/{lang}/product/{id}
      const response = await apiClient.getWithLang<any>(API_ENDPOINTS.PRODUCTS.BY_ID, { id: productId });
      console.log('✅ Produit récupéré:', response);
      return response;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du produit:', error);
      throw error;
    }
  }

  /**
   * Récupère les nouveaux arrivages - comme le client web
   */
  async getNewArrivals() {
    console.log('🆕 EcommerceApiService.getNewArrivals');
    // Utilise /products/showing car /products/new-arrivals n'existe pas dans l'API Gateway
    return apiClient.getWithLang<Product[]>(API_ENDPOINTS.PRODUCTS.SHOWING);
  }

  /**
   * Récupère les produits similaires
   */
  async getRelatedProducts(id: string) {
    return apiClient.getWithLang<Product[]>(API_ENDPOINTS.PRODUCTS.RELATED, { id });
  }

  /**
   * Recherche de produits
   */
  async searchProducts(query: string, filters?: any) {
    return apiClient.getWithLang<{ products: Product[]; total: number }>(
      API_ENDPOINTS.PRODUCTS.SEARCH,
      undefined,
      { params: { q: query, ...filters } }
    );
  }

  /**
   * Récupère les catégories (toutes)
   */
  async getCategories() {
    console.log('📂 EcommerceApiService.getCategories');
    return apiClient.getWithLang<Category[]>(API_ENDPOINTS.PRODUCTS.CATEGORIES);
  }

  /**
   * Récupère les catégories affichées (showing) - EXACTEMENT comme CategoryServices.getAllCategory du client web
   */
  async getShowingCategories() {
    console.log('📂 EcommerceApiService.getShowingCategories - comme CategoryServices.getAllCategory');
    // Utilise exactement le même endpoint que le client web
    return apiClient.getWithLang<Category[]>('/v1/{lang}/categories/showing');
  }

  /**
   * Récupère les catégories en vedette - comme le client web
   */
  async getFeaturedCategories() {
    console.log('⭐ EcommerceApiService.getFeaturedCategories');
    return apiClient.getWithLang<Category[]>('/v1/{lang}/categories/featured');
  }

  /**
   * Récupère les catégories avec produits pour la page d'accueil - EXACTEMENT comme CategoryServices.getShowingProductsOnHomePageCategories du client web
   */
  async getShowingProductsOnHomePageCategories() {
    console.log('🏠 EcommerceApiService.getShowingProductsOnHomePageCategories - comme CategoryServices');
    // Utilise exactement le même endpoint que le client web (ligne 19 de CategoryServices.ts)
    return apiClient.getWithLang<Category[]>('/v1/{lang}/categories/showingProductsOnHomePage');
  }

  /**
   * Récupère les catégories avec leurs produits - comme le client web
   */
  async getCategoriesWithProducts() {
    console.log('📦 EcommerceApiService.getCategoriesWithProducts');
    // Utilise /categories/showing car pas de route spécifique dans l'API Gateway
    return apiClient.getWithLang<any[]>('/v1/{lang}/categories/showing');
  }

  // ===== SETTINGS =====

  /**
   * Récupère les paramètres globaux - pour compatibilité avec settingsSlice
   */
  async getGlobalSettings() {
    console.log('⚙️ EcommerceApiService.getGlobalSettings');
    return apiClient.getWithLang<any>('/v1/{lang}/setting/global');
  }

  /**
   * Récupère les paramètres de personnalisation du magasin - pour compatibilité avec settingsSlice
   */
  async getStoreCustomizationSettings() {
    console.log('🎨 EcommerceApiService.getStoreCustomizationSettings');
    return apiClient.getWithLang<any>('/v1/{lang}/setting/store/customization');
  }

  // ===== PANIER =====

  /**
   * Récupère le panier de l'utilisateur
   */
  async getCart() {
    return apiClient.getWithLang<Cart>(API_ENDPOINTS.CART.GET);
  }

  /**
   * Ajoute un produit au panier
   */
  async addToCart(productId: string, quantity: number, variantId?: string) {
    return apiClient.postWithLang<Cart>(API_ENDPOINTS.CART.ADD, {
      productId,
      quantity,
      variantId,
    });
  }

  /**
   * Met à jour la quantité d'un produit dans le panier
   */
  async updateCartItem(itemId: string, quantity: number) {
    return apiClient.putWithLang<Cart>(API_ENDPOINTS.CART.UPDATE, {
      itemId,
      quantity,
    });
  }

  /**
   * Supprime un produit du panier
   */
  async removeFromCart(itemId: string) {
    return apiClient.deleteWithLang<Cart>(API_ENDPOINTS.CART.REMOVE, { itemId });
  }

  /**
   * Vide le panier
   */
  async clearCart() {
    return apiClient.deleteWithLang<void>(API_ENDPOINTS.CART.CLEAR);
  }

  // ===== COMMANDES =====

  /**
   * Récupère les commandes de l'utilisateur
   */
  async getOrders(params?: { page?: number; limit?: number; status?: OrderStatus }) {
    return apiClient.get<{ orders: Order[]; total: number; page: number; totalPages: number }>(
      API_ENDPOINTS.ORDERS.LIST,
      { params }
    );
  }

  /**
   * Crée une nouvelle commande
   */
  async createOrder(orderData: {
    items: { productId: string; quantity: number; variantId?: string }[];
    shippingAddress: Address;
    billingAddress: Address;
    paymentMethod: string;
  }) {
    return apiClient.post<Order>(API_ENDPOINTS.ORDERS.CREATE, orderData);
  }

  /**
   * Récupère les détails d'une commande
   */
  async getOrder(id: string) {
    return apiClient.get<Order>(buildUrl(API_ENDPOINTS.ORDERS.DETAIL, { id }));
  }

  /**
   * Annule une commande
   */
  async cancelOrder(id: string, reason?: string) {
    return apiClient.post<Order>(buildUrl(API_ENDPOINTS.ORDERS.CANCEL, { id }), { reason });
  }

  /**
   * Suit une commande
   */
  async trackOrder(id: string) {
    return apiClient.get<{ status: OrderStatus; trackingNumber?: string; history: any[] }>(
      buildUrl(API_ENDPOINTS.ORDERS.TRACK, { id })
    );
  }

  // ===== LISTE DE SOUHAITS =====

  /**
   * Récupère la liste de souhaits
   */
  async getWishlist() {
    return apiClient.get<Product[]>(API_ENDPOINTS.USER.WISHLIST);
  }

  /**
   * Ajoute un produit à la liste de souhaits
   */
  async addToWishlist(productId: string) {
    return apiClient.post<void>(API_ENDPOINTS.USER.WISHLIST, { productId });
  }

  /**
   * Supprime un produit de la liste de souhaits
   */
  async removeFromWishlist(productId: string) {
    return apiClient.delete<void>(`${API_ENDPOINTS.USER.WISHLIST}/${productId}`);
  }

  // ===== ADRESSES =====

  /**
   * Récupère les adresses de l'utilisateur
   */
  async getAddresses() {
    return apiClient.get<Address[]>(API_ENDPOINTS.USER.ADDRESSES);
  }

  /**
   * Ajoute une nouvelle adresse
   */
  async addAddress(address: Omit<Address, 'id'>) {
    return apiClient.post<Address>(API_ENDPOINTS.USER.ADDRESSES, address);
  }

  /**
   * Met à jour une adresse
   */
  async updateAddress(id: string, address: Partial<Address>) {
    return apiClient.put<Address>(`${API_ENDPOINTS.USER.ADDRESSES}/${id}`, address);
  }

  /**
   * Supprime une adresse
   */
  async deleteAddress(id: string) {
    return apiClient.delete<void>(`${API_ENDPOINTS.USER.ADDRESSES}/${id}`);
  }
}

// Instance singleton
export const ecommerceApi = new EcommerceApiService();
export const ecommerceService = ecommerceApi; // Alias pour compatibilité
