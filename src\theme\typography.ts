/**
 * Système de Typographie E-Luxe Mobile
 * Synchronisé avec le client web E-Luxe
 * Polices et styles de texte luxueux
 */

import { Platform } from 'react-native';

// Polices système optimisées pour chaque plateforme
export const FontFamilies = {
  // Police principale (élégante et lisible)
  primary: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),

  // Police secondaire (pour les titres)
  secondary: Platform.select({
    ios: 'Georgia',
    android: 'serif',
    default: 'serif',
  }),

  // Police monospace (pour les codes, prix)
  monospace: Platform.select({
    ios: 'Menlo',
    android: 'monospace',
    default: 'monospace',
  }),

  // Police luxueuse (pour les éléments premium)
  luxury: Platform.select({
    ios: 'Didot',
    android: 'serif',
    default: 'serif',
  }),
};

// Tailles de police (échelle harmonieuse)
export const FontSizes = {
  // Très petites tailles
  xs: 10,
  sm: 12,

  // Tailles de base
  base: 14,
  md: 16,
  lg: 18,

  // Grandes tailles
  xl: 20,
  '2xl': 24,
  '3xl': 28,
  '4xl': 32,
  '5xl': 36,
  '6xl': 42,
  '7xl': 48,
  '8xl': 56,
  '9xl': 64,
};

// Poids de police
export const FontWeights = {
  thin: '100',
  extraLight: '200',
  light: '300',
  normal: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
  extraBold: '800',
  black: '900',
} as const;

// Hauteurs de ligne
export const LineHeights = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
};

// Espacement des lettres
export const LetterSpacings = {
  tighter: -0.5,
  tight: -0.25,
  normal: 0,
  wide: 0.25,
  wider: 0.5,
  widest: 1,
};

// Styles de texte prédéfinis (EXACTEMENT comme le client web E-Luxe)
export const TextStyles = {
  // Titres principaux (comme tpsectionarea__title: 40px, font-weight: 600)
  h1: {
    fontFamily: FontFamilies.luxury,
    fontSize: 40, // Exactement comme le web
    fontWeight: FontWeights.semiBold, // 600 comme le web
    lineHeight: 48,
    letterSpacing: LetterSpacings.tight,
  },

  h2: {
    fontFamily: FontFamilies.luxury,
    fontSize: FontSizes['3xl'], // 32px
    fontWeight: FontWeights.semiBold, // 600 comme le web
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacings.tight,
  },
  
  h3: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.snug,
    letterSpacing: LetterSpacings.normal,
  },

  h4: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.snug,
    letterSpacing: LetterSpacings.normal,
  },

  h5: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },
  
  h6: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.md,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },

  // Corps de texte
  body: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.relaxed,
    letterSpacing: LetterSpacings.normal,
  },

  bodyLarge: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.md,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.relaxed,
    letterSpacing: LetterSpacings.normal,
  },

  bodySmall: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },

  // Textes spéciaux
  caption: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.wide,
  },
  
  overline: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.widest,
    textTransform: 'uppercase' as const,
  },

  // Boutons
  button: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.wide,
  },

  buttonLarge: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.md,
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.wide,
  },

  buttonSmall: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.normal,
  },

  // Prix et valeurs monétaires
  price: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.normal,
  },
  
  priceSmall: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.normal,
  },
  
  priceLarge: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.tight,
  },

  // Produits
  productTitle: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },

  // Navigation
  tabLabel: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.normal,
  },
  
  navTitle: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semiBold,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.normal,
  },

  // Formulaires
  label: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },
  
  input: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },
  
  placeholder: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacings.normal,
  },

  // Éléments luxueux
  luxury: {
    fontFamily: FontFamilies.luxury,
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacings.wide,
  },
  
  brand: {
    fontFamily: FontFamilies.luxury,
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.normal,
    lineHeight: LineHeights.none,
    letterSpacing: LetterSpacings.wider,
  },
};

export default TextStyles;
