/**
 * Composant d'Icône Centralisé - E-Luxe Mobile
 * Gère tous les types d'icônes avec une interface unifiée
 */

import React from 'react';
import { ViewStyle, TextStyle } from 'react-native';

// Import des différentes bibliothèques d'icônes
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Feather from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';

import { ELuxeColors } from '../../theme/colors';

// Types d'icônes supportés
export type IconType = 
  | 'material'
  | 'material-community'
  | 'font-awesome'
  | 'font-awesome-5'
  | 'ionicons'
  | 'feather'
  | 'ant-design'
  | 'entypo';

// Tailles prédéfinies
export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | number;

const ICON_SIZES: Record<Exclude<IconSize, number>, number> = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 28,
  '2xl': 32,
};

// Props du composant Icon
export interface IconProps {
  type: IconType;
  name: string;
  size?: IconSize;
  color?: string;
  style?: ViewStyle | TextStyle;
  onPress?: () => void;
}

/**
 * Composant Icon unifié
 */
const Icon: React.FC<IconProps> = ({
  type,
  name,
  size = 'md',
  color = ELuxeColors.textPrimary,
  style,
  onPress,
}) => {
  // Calculer la taille finale
  const finalSize = typeof size === 'number' ? size : ICON_SIZES[size];

  // Props communes pour tous les types d'icônes
  const commonProps = {
    name,
    size: finalSize,
    color,
    style,
    onPress,
  };

  // Sélectionner le bon composant d'icône
  switch (type) {
    case 'material':
      return <MaterialIcons {...commonProps} />;
    
    case 'material-community':
      return <MaterialCommunityIcons {...commonProps} />;
    
    case 'font-awesome':
      return <FontAwesome {...commonProps} />;
    
    case 'font-awesome-5':
      return <FontAwesome5 {...commonProps} />;
    
    case 'ionicons':
      return <Ionicons {...commonProps} />;
    
    case 'feather':
      return <Feather {...commonProps} />;
    
    case 'ant-design':
      return <AntDesign {...commonProps} />;
    
    case 'entypo':
      return <Entypo {...commonProps} />;
    
    default:
      // Fallback vers MaterialIcons
      return <MaterialIcons {...commonProps} />;
  }
};

// Icônes prédéfinies pour E-Luxe (avec les bonnes bibliothèques)
export const ELuxeIcons = {
  // Navigation
  home: { type: 'material' as IconType, name: 'home' },
  search: { type: 'material' as IconType, name: 'search' },
  categories: { type: 'material' as IconType, name: 'category' },
  cart: { type: 'material' as IconType, name: 'shopping-cart' },
  profile: { type: 'material' as IconType, name: 'person' },
  menu: { type: 'material' as IconType, name: 'menu' },
  back: { type: 'material' as IconType, name: 'arrow-back' },
  close: { type: 'material' as IconType, name: 'close' },
  grid: { type: 'material' as IconType, name: 'grid-view' },

  // E-commerce
  favorite: { type: 'material' as IconType, name: 'favorite' },
  favoriteOutline: { type: 'material' as IconType, name: 'favorite-border' },
  star: { type: 'material' as IconType, name: 'star' },
  starOutline: { type: 'material' as IconType, name: 'star-border' },
  add: { type: 'material' as IconType, name: 'add' },
  remove: { type: 'material' as IconType, name: 'remove' },
  addToCart: { type: 'material' as IconType, name: 'add-shopping-cart' },
  
  // Actions
  edit: { type: 'material' as IconType, name: 'edit' },
  delete: { type: 'material' as IconType, name: 'delete' },
  share: { type: 'material' as IconType, name: 'share' },
  download: { type: 'material' as IconType, name: 'download' },
  upload: { type: 'material' as IconType, name: 'upload' },
  
  // Interface
  clear: { type: 'material' as IconType, name: 'clear' },
  check: { type: 'material' as IconType, name: 'check' },
  checkCircle: { type: 'material' as IconType, name: 'check-circle' },
  error: { type: 'material' as IconType, name: 'error' },
  warning: { type: 'material' as IconType, name: 'warning' },
  info: { type: 'material' as IconType, name: 'info' },
  
  // Flèches et navigation
  arrowForward: { type: 'material' as IconType, name: 'arrow-forward' },
  arrowBack: { type: 'material' as IconType, name: 'arrow-back' },
  arrowUp: { type: 'material' as IconType, name: 'keyboard-arrow-up' },
  arrowDown: { type: 'material' as IconType, name: 'keyboard-arrow-down' },
  arrowLeft: { type: 'material' as IconType, name: 'keyboard-arrow-left' },
  arrowRight: { type: 'material' as IconType, name: 'keyboard-arrow-right' },
  
  // Filtres et tri
  filter: { type: 'material' as IconType, name: 'filter-list' },
  sort: { type: 'material' as IconType, name: 'sort' },
  grid: { type: 'material' as IconType, name: 'grid-view' },
  list: { type: 'material' as IconType, name: 'list' },
  
  // Utilisateur
  login: { type: 'material' as IconType, name: 'login' },
  logout: { type: 'material' as IconType, name: 'logout' },
  settings: { type: 'material' as IconType, name: 'settings' },
  account: { type: 'material' as IconType, name: 'account-circle' },
  
  // Communication
  email: { type: 'material' as IconType, name: 'email' },
  phone: { type: 'material' as IconType, name: 'phone' },
  message: { type: 'material' as IconType, name: 'message' },
  
  // Luxe et premium (FontAwesome pour plus de style)
  crown: { type: 'font-awesome-5' as IconType, name: 'crown' },
  gem: { type: 'font-awesome-5' as IconType, name: 'gem' },
  award: { type: 'font-awesome-5' as IconType, name: 'award' },
  medal: { type: 'font-awesome-5' as IconType, name: 'medal' },
  diamond: { type: 'font-awesome' as IconType, name: 'diamond' },
  
  // Paiement
  creditCard: { type: 'material' as IconType, name: 'credit-card' },
  payment: { type: 'material' as IconType, name: 'payment' },
  wallet: { type: 'ionicons' as IconType, name: 'wallet-outline' },
  
  // Livraison
  delivery: { type: 'material' as IconType, name: 'local-shipping' },
  location: { type: 'material' as IconType, name: 'location-on' },
  track: { type: 'material' as IconType, name: 'track-changes' },
};

// Composant d'icône E-Luxe avec icônes prédéfinies
export interface ELuxeIconProps extends Omit<IconProps, 'type' | 'name'> {
  icon: keyof typeof ELuxeIcons;
}

export const ELuxeIcon: React.FC<ELuxeIconProps> = ({ icon, ...props }) => {
  const iconConfig = ELuxeIcons[icon];
  return <Icon type={iconConfig.type} name={iconConfig.name} {...props} />;
};

export default Icon;
