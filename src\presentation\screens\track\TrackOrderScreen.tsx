import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Toast from 'react-native-toast-message';

interface TrackingStep {
  id: string;
  title: string;
  description: string;
  timestamp: Date;
  status: 'completed' | 'current' | 'pending';
  location?: string;
}

interface OrderTracking {
  orderId: string;
  trackingNumber: string;
  status: string;
  estimatedDelivery: Date;
  carrier: string;
  steps: TrackingStep[];
}

const TrackOrderScreen: React.FC = () => {
  const [trackingNumber, setTrackingNumber] = useState('');
  const [orderTracking, setOrderTracking] = useState<OrderTracking | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Mock tracking data
  const mockTrackingData: OrderTracking = {
    orderId: 'EL-2024-001234',
    trackingNumber: 'TRK123456789',
    status: 'In Transit',
    estimatedDelivery: new Date('2024-01-20'),
    carrier: 'Express Delivery',
    steps: [
      {
        id: '1',
        title: 'Order Confirmed',
        description: 'Your order has been confirmed and is being prepared',
        timestamp: new Date('2024-01-15T10:00:00'),
        status: 'completed',
        location: 'E-Luxe Warehouse',
      },
      {
        id: '2',
        title: 'Order Packed',
        description: 'Your items have been carefully packed',
        timestamp: new Date('2024-01-15T14:30:00'),
        status: 'completed',
        location: 'E-Luxe Warehouse',
      },
      {
        id: '3',
        title: 'Shipped',
        description: 'Your order has been shipped and is on its way',
        timestamp: new Date('2024-01-16T09:15:00'),
        status: 'completed',
        location: 'Distribution Center',
      },
      {
        id: '4',
        title: 'In Transit',
        description: 'Your package is currently in transit to your location',
        timestamp: new Date('2024-01-17T11:45:00'),
        status: 'current',
        location: 'Regional Hub',
      },
      {
        id: '5',
        title: 'Out for Delivery',
        description: 'Your package is out for delivery',
        timestamp: new Date(),
        status: 'pending',
        location: 'Local Delivery Center',
      },
      {
        id: '6',
        title: 'Delivered',
        description: 'Your package has been delivered',
        timestamp: new Date(),
        status: 'pending',
        location: 'Your Address',
      },
    ],
  };

  const handleTrackOrder = async () => {
    if (!trackingNumber.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Validation Error',
        text2: 'Please enter a tracking number',
      });
      return;
    }

    setIsLoading(true);
    setHasSearched(true);

    try {
      // TODO: Replace with real API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate tracking number validation
      if (trackingNumber.toLowerCase().includes('trk')) {
        setOrderTracking(mockTrackingData);
      } else {
        setOrderTracking(null);
        Toast.show({
          type: 'error',
          text1: 'Order Not Found',
          text2: 'Please check your tracking number and try again',
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to track order. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStepIcon = (status: TrackingStep['status']) => {
    switch (status) {
      case 'completed':
        return 'check-circle';
      case 'current':
        return 'radio-button-checked';
      case 'pending':
        return 'radio-button-unchecked';
      default:
        return 'radio-button-unchecked';
    }
  };

  const getStepColor = (status: TrackingStep['status']) => {
    switch (status) {
      case 'completed':
        return '#4CAF50';
      case 'current':
        return '#007AFF';
      case 'pending':
        return '#E0E0E0';
      default:
        return '#E0E0E0';
    }
  };

  const renderTrackingStep = (step: TrackingStep, index: number) => {
    const isLast = index === orderTracking!.steps.length - 1;
    
    return (
      <View key={step.id} style={styles.trackingStep}>
        <View style={styles.stepIndicator}>
          <Icon
            name={getStepIcon(step.status)}
            size={24}
            color={getStepColor(step.status)}
          />
          {!isLast && (
            <View
              style={[
                styles.stepLine,
                { backgroundColor: step.status === 'completed' ? '#4CAF50' : '#E0E0E0' }
              ]}
            />
          )}
        </View>
        
        <View style={styles.stepContent}>
          <Text style={[
            styles.stepTitle,
            step.status === 'current' && styles.currentStepTitle
          ]}>
            {step.title}
          </Text>
          <Text style={styles.stepDescription}>{step.description}</Text>
          {step.location && (
            <Text style={styles.stepLocation}>📍 {step.location}</Text>
          )}
          {step.status !== 'pending' && (
            <Text style={styles.stepTimestamp}>
              {step.timestamp.toLocaleDateString()} at {step.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderOrderInfo = () => {
    if (!orderTracking) return null;

    return (
      <View style={styles.orderInfoCard}>
        <View style={styles.orderHeader}>
          <Text style={styles.orderTitle}>Order #{orderTracking.orderId}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(orderTracking.status) }]}>
            <Text style={styles.statusText}>{orderTracking.status}</Text>
          </View>
        </View>
        
        <View style={styles.orderDetails}>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Tracking Number:</Text>
            <Text style={styles.orderDetailValue}>{orderTracking.trackingNumber}</Text>
          </View>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Carrier:</Text>
            <Text style={styles.orderDetailValue}>{orderTracking.carrier}</Text>
          </View>
          <View style={styles.orderDetailRow}>
            <Text style={styles.orderDetailLabel}>Estimated Delivery:</Text>
            <Text style={styles.orderDetailValue}>
              {orderTracking.estimatedDelivery.toLocaleDateString()}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return '#4CAF50';
      case 'in transit':
      case 'shipped':
        return '#007AFF';
      case 'processing':
        return '#FF9500';
      default:
        return '#666';
    }
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="local-shipping" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>Track Your Order</Text>
      <Text style={styles.emptySubtitle}>
        Enter your tracking number to see the latest updates on your order
      </Text>
    </View>
  );

  const renderNotFound = () => (
    <View style={styles.emptyState}>
      <Icon name="search-off" size={64} color="#ccc" />
      <Text style={styles.emptyTitle}>Order Not Found</Text>
      <Text style={styles.emptySubtitle}>
        We couldn't find an order with that tracking number. Please check and try again.
      </Text>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Track Your Order</Text>
        <Text style={styles.headerSubtitle}>
          Enter your tracking number to get real-time updates
        </Text>
      </View>

      {/* Search Section */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            value={trackingNumber}
            onChangeText={setTrackingNumber}
            placeholder="Enter tracking number (e.g., TRK123456789)"
            placeholderTextColor="#999"
            autoCapitalize="characters"
          />
          <TouchableOpacity
            style={[styles.searchButton, isLoading && styles.searchButtonDisabled]}
            onPress={handleTrackOrder}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Icon name="search" size={20} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        </View>
      </View>

      {/* Results */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Tracking your order...</Text>
        </View>
      ) : orderTracking ? (
        <View style={styles.resultsContainer}>
          {renderOrderInfo()}
          
          <View style={styles.trackingContainer}>
            <Text style={styles.trackingTitle}>Tracking History</Text>
            {orderTracking.steps.map((step, index) => renderTrackingStep(step, index))}
          </View>
        </View>
      ) : hasSearched ? (
        renderNotFound()
      ) : (
        renderEmptyState()
      )}

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#000',
    backgroundColor: '#FFFFFF',
    marginRight: 12,
  },
  searchButton: {
    width: 48,
    height: 48,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchButtonDisabled: {
    backgroundColor: '#999',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  resultsContainer: {
    paddingHorizontal: 20,
  },
  orderInfoCard: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  orderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  orderDetails: {
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: 16,
  },
  orderDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderDetailLabel: {
    fontSize: 14,
    color: '#666',
  },
  orderDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000',
  },
  trackingContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  trackingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 20,
  },
  trackingStep: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  stepIndicator: {
    alignItems: 'center',
    marginRight: 16,
  },
  stepLine: {
    width: 2,
    height: 40,
    marginTop: 8,
  },
  stepContent: {
    flex: 1,
    paddingBottom: 8,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    marginBottom: 4,
  },
  currentStepTitle: {
    color: '#007AFF',
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 4,
  },
  stepLocation: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  stepTimestamp: {
    fontSize: 12,
    color: '#999',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default TrackOrderScreen;
