import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import FastImage from 'react-native-fast-image';

interface Category {
  id: string;
  name: string;
  image: string;
}

interface CategoryCardProps {
  category: Category;
  onPress: () => void;
  style?: ViewStyle;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  onPress,
  style,
}) => {
  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
      <View style={styles.imageContainer}>
        {/* Gestion d'image EXACTEMENT comme le client web (CategoryTable.tsx) */}
        <FastImage
          source={{
            uri: (category.image && category.image.trim() !== '')
              ? category.image
              : 'https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png'
          }}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
          onError={() => {
            console.log('⚠️ Erreur chargement image catégorie:', category.name);
          }}
        />
      </View>
      <Text style={styles.name} numberOfLines={2}>
        {category.name}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginRight: 16,
    width: 80,
  },
  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F2F2F7',
    marginBottom: 8,
    overflow: 'hidden',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  name: {
    fontSize: 12,
    fontWeight: '500',
    color: '#000000',
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default CategoryCard;
