/**
 * Configuration i18n pour Mobile E-Luxe 1.0
 * Reproduction exacte de la configuration du client web
 * Compatible avec l'architecture multilingue E-Luxe (en, fr)
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import des traductions (comme le client web)
import en from './translations/en.json';
import fr from './translations/fr.json';

// Fonction pour obtenir la langue du système (version simplifiée)
const getSystemLanguage = (): string => {
  // Pour l'instant, on utilise 'en' par défaut
  // TODO: Ajouter détection système plus tard si nécessaire
  return 'en';
};

// Configuration SIMPLIFIÉE pour éviter les conflits de dépendances
i18n
  .use(initReactI18next) // Intégration React
  .init({
    // Ressources de traduction (EXACTEMENT comme le client web)
    resources: {
      en: { translation: en },
      fr: { translation: fr },
    },

    // Configuration identique au client web
    debug: __DEV__, // Debug en mode développement seulement
    lng: getSystemLanguage(), // Langue initiale depuis le système
    fallbackLng: 'en', // Langue de fallback (comme le client web)

    // Interpolation (comme le client web)
    interpolation: {
      escapeValue: false, // React échappe déjà les valeurs
    },

    // Compatibilité avec l'API E-Luxe
    compatibilityJSON: 'v3',
  });

// Fonction pour obtenir la langue actuelle (comme le client web)
export const getCurrentLanguage = (): string => {
  return i18n.language || 'en';
};

// Fonction pour changer la langue (comme le client web)
export const changeLanguage = async (language: string): Promise<void> => {
  await i18n.changeLanguage(language);
};

// Fonction pour obtenir les langues supportées (comme le client web)
export const getSupportedLanguages = (): string[] => {
  return ['en', 'fr'];
};

// Fonction pour vérifier si une langue est supportée
export const isLanguageSupported = (language: string): boolean => {
  return getSupportedLanguages().includes(language);
};

// Export de la fonction getSystemLanguage
export { getSystemLanguage };

// Export par défaut
export default i18n;
