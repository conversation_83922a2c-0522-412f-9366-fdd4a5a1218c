import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { RootStackParamList } from '../../navigation/AppNavigator';
import ProductCard from '../../components/ProductCard';

type SearchScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Search'>;
type SearchScreenRouteProp = {
  params: {
    query?: string;
  };
};

const SearchScreen: React.FC = () => {
  const navigation = useNavigation<SearchScreenNavigationProp>();
  const route = useRoute<SearchScreenRouteProp>();
  const dispatch = useAppDispatch();
  
  const { products, isLoading } = useAppSelector((state) => state.product);
  
  const [searchQuery, setSearchQuery] = useState(route.params?.query || '');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'wireless headphones',
    'smart watch',
    'bluetooth speaker',
    'gaming mouse',
  ]);
  const [popularSearches] = useState<string[]>([
    'iPhone',
    'MacBook',
    'AirPods',
    'iPad',
    'Samsung Galaxy',
    'PlayStation',
    'Nintendo Switch',
    'Apple Watch',
  ]);
  const [isSearching, setIsSearching] = useState(false);

  // Mock search results
  const mockSearchResults = [
    {
      id: '1',
      title: 'Premium Wireless Headphones',
      price: 299.99,
      originalPrice: 399.99,
      image: 'https://via.placeholder.com/200x200/007AFF/FFFFFF?text=Headphones',
      rating: 4.8,
      discount: 25,
    },
    {
      id: '2',
      title: 'Smart Fitness Watch',
      price: 199.99,
      image: 'https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text=Watch',
      rating: 4.6,
    },
    {
      id: '3',
      title: 'Bluetooth Speaker',
      price: 89.99,
      originalPrice: 129.99,
      image: 'https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=Speaker',
      rating: 4.7,
      discount: 31,
    },
  ];

  useEffect(() => {
    if (route.params?.query) {
      handleSearch(route.params.query);
    }
  }, [route.params?.query]);

  const handleSearch = async (query: string) => {
    if (!query.trim()) return;

    setIsSearching(true);
    Keyboard.dismiss();

    try {
      // TODO: Dispatch search action
      // await dispatch(searchProductsAsync({ query: query.trim() }));
      
      // Mock search delay
      setTimeout(() => {
        setSearchResults(mockSearchResults);
        setIsSearching(false);
        
        // Add to recent searches
        setRecentSearches(prev => {
          const filtered = prev.filter(item => item !== query.trim());
          return [query.trim(), ...filtered].slice(0, 10);
        });
      }, 1000);
    } catch (error) {
      console.error('Search failed:', error);
      setIsSearching(false);
    }
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetail', { productId });
  };

  const handleAddToCart = (product: any) => {
    // TODO: Implement add to cart
    console.log('Add to cart:', product);
  };

  const handleToggleWishlist = (product: any) => {
    // TODO: Implement wishlist toggle
    console.log('Toggle wishlist:', product);
  };

  const handleRecentSearchPress = (query: string) => {
    setSearchQuery(query);
    handleSearch(query);
  };

  const handlePopularSearchPress = (query: string) => {
    setSearchQuery(query);
    handleSearch(query);
  };

  const handleClearRecentSearches = () => {
    setRecentSearches([]);
  };

  const handleRemoveRecentSearch = (query: string) => {
    setRecentSearches(prev => prev.filter(item => item !== query));
  };

  const renderSearchHeader = () => (
    <View style={styles.searchHeader}>
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => handleSearch(searchQuery)}
          returnKeyType="search"
          autoFocus={!route.params?.query}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => {
              setSearchQuery('');
              setSearchResults([]);
            }}
          >
            <Icon name="clear" size={20} color="#666666" />
          </TouchableOpacity>
        )}
      </View>
      
      <TouchableOpacity
        style={styles.cancelButton}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.cancelText}>Cancel</Text>
      </TouchableOpacity>
    </View>
  );

  const renderRecentSearches = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Recent Searches</Text>
        {recentSearches.length > 0 && (
          <TouchableOpacity onPress={handleClearRecentSearches}>
            <Text style={styles.clearAllText}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {recentSearches.length === 0 ? (
        <Text style={styles.emptyText}>No recent searches</Text>
      ) : (
        <View style={styles.searchList}>
          {recentSearches.map((query, index) => (
            <View key={index} style={styles.searchItem}>
              <TouchableOpacity
                style={styles.searchItemContent}
                onPress={() => handleRecentSearchPress(query)}
              >
                <Icon name="history" size={20} color="#666666" />
                <Text style={styles.searchItemText}>{query}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveRecentSearch(query)}
              >
                <Icon name="close" size={16} color="#999999" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderPopularSearches = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Popular Searches</Text>
      <View style={styles.tagsContainer}>
        {popularSearches.map((query, index) => (
          <TouchableOpacity
            key={index}
            style={styles.tag}
            onPress={() => handlePopularSearchPress(query)}
          >
            <Text style={styles.tagText}>{query}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSearchResults = () => {
    if (isSearching) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      );
    }

    if (searchResults.length === 0 && searchQuery.trim()) {
      return (
        <View style={styles.emptyContainer}>
          <Icon name="search-off" size={80} color="#C7C7CC" />
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptySubtitle}>
            Try searching with different keywords
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsHeader}>
          {searchResults.length} results for "{searchQuery}"
        </Text>
        <FlatList
          data={searchResults}
          renderItem={({ item, index }) => (
            <View style={[styles.productContainer, index % 2 === 1 && styles.productContainerRight]}>
              <ProductCard
                product={item}
                onPress={() => handleProductPress(item.id)}
                onAddToCart={() => handleAddToCart(item)}
                onToggleWishlist={() => handleToggleWishlist(item)}
                isInWishlist={false}
                showAddToCart={true}
              />
            </View>
          )}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.resultsGrid}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };

  const showSearchSuggestions = !searchQuery.trim() || (!isSearching && searchResults.length === 0 && !searchQuery.trim());

  return (
    <View style={styles.container}>
      {renderSearchHeader()}
      
      {showSearchSuggestions ? (
        <View style={styles.suggestionsContainer}>
          {renderRecentSearches()}
          {renderPopularSearches()}
        </View>
      ) : (
        renderSearchResults()
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 44,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
  },
  clearButton: {
    padding: 4,
  },
  cancelButton: {
    paddingVertical: 8,
  },
  cancelText: {
    fontSize: 16,
    color: '#007AFF',
  },
  suggestionsContainer: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  clearAllText: {
    fontSize: 14,
    color: '#007AFF',
  },
  emptyText: {
    fontSize: 14,
    color: '#999999',
    fontStyle: 'italic',
  },
  searchList: {
    marginTop: 8,
  },
  searchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  searchItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchItemText: {
    fontSize: 14,
    color: '#000000',
    marginLeft: 12,
  },
  removeButton: {
    padding: 4,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#F2F2F7',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    color: '#666666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 20,
  },
  resultsContainer: {
    flex: 1,
  },
  resultsHeader: {
    fontSize: 14,
    color: '#666666',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  resultsGrid: {
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  productContainer: {
    flex: 1,
    marginHorizontal: 4,
    marginVertical: 4,
  },
  productContainerRight: {
    marginLeft: 8,
  },
});

export default SearchScreen;
