#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007fff5db457e0, pid=49672, tid=52684
#
# JRE version: Java(TM) SE Runtime Environment (17.0.11+7) (build 17.0.11+7-LTS-207)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# C  [net.dll+0x57e0]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  S U M M A R Y ------------

Command Line: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew org.gradle.wrapper.GradleWrapperMain tasks

Host: Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sat Jun 21 04:52:23 2025 Mauritius Standard Time elapsed time: 26.215842 seconds (0d 0h 0m 26s)

---------------  T H R E A D  ---------------

Current thread (0x0000016c3cd25260):  JavaThread "main" [_thread_in_native, id=52684, stack(0x0000005c83500000,0x0000005c83600000)]

Stack: [0x0000005c83500000,0x0000005c83600000],  sp=0x0000005c835fc550,  free space=1009k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [net.dll+0x57e0]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.net.NetworkInterface.getAll()[Ljava/net/NetworkInterface;+0 java.base@17.0.11
j  java.net.NetworkInterface.getNetworkInterfaces()Ljava/util/Enumeration;+0 java.base@17.0.11
j  org.gradle.internal.remote.internal.inet.InetAddresses.analyzeNetworkInterfaces()V+0
j  org.gradle.internal.remote.internal.inet.InetAddresses.<init>()V+38
j  org.gradle.internal.remote.internal.inet.InetAddressFactory.init()V+25
j  org.gradle.internal.remote.internal.inet.InetAddressFactory.getWildcardBindingAddress()Ljava/net/InetAddress;+8
j  org.gradle.cache.internal.locklistener.FileLockCommunicator.<init>(Lorg/gradle/internal/remote/internal/inet/InetAddressFactory;)V+16
j  org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler.getCommunicator()Lorg/gradle/cache/internal/locklistener/FileLockCommunicator;+29
j  org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler.reservePort()I+1
j  org.gradle.cache.internal.DefaultFileLockManager.lock(Ljava/io/File;Lorg/gradle/cache/LockOptions;Ljava/lang/String;Ljava/lang/String;Lorg/gradle/api/Action;)Lorg/gradle/cache/FileLock;+78
j  org.gradle.cache.internal.DefaultFileLockManager.lock(Ljava/io/File;Lorg/gradle/cache/LockOptions;Ljava/lang/String;Ljava/lang/String;)Lorg/gradle/cache/FileLock;+7
j  org.gradle.cache.internal.DefaultFileLockManager.lock(Ljava/io/File;Lorg/gradle/cache/LockOptions;Ljava/lang/String;)Lorg/gradle/cache/FileLock;+6
j  org.gradle.cache.internal.OnDemandFileAccess.readFile(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+18
j  org.gradle.cache.internal.SimpleStateCache.get()Ljava/lang/Object;+12
j  org.gradle.cache.internal.FileIntegrityViolationSuppressingPersistentStateCacheDecorator.get()Ljava/lang/Object;+4
j  org.gradle.launcher.daemon.registry.PersistentDaemonRegistry.getAll()Ljava/util/List;+13
j  org.gradle.launcher.daemon.client.DefaultDaemonConnector.connect(Lorg/gradle/api/internal/specs/ExplainingSpec;)Lorg/gradle/launcher/daemon/client/DaemonClientConnection;+5
j  org.gradle.launcher.daemon.client.DaemonClient.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+62
j  org.gradle.launcher.daemon.client.DaemonClient.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.launcher.cli.RunBuildAction.run()V+74
j  org.gradle.internal.Actions$RunnableActionAdapter.execute(Ljava/lang/Object;)V+4
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$ParseAndBuildAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+60
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$ParseAndBuildAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.DebugLoggerWarningAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+9
j  org.gradle.launcher.cli.DebugLoggerWarningAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.WelcomeMessageAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+218
j  org.gradle.launcher.cli.WelcomeMessageAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.NativeServicesInitializingAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+35
j  org.gradle.launcher.cli.NativeServicesInitializingAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.ExceptionReportingAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+5
j  org.gradle.launcher.cli.ExceptionReportingAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$WithLogging.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+299
j  org.gradle.launcher.Main.doAction([Ljava/lang/String;Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+14
j  org.gradle.launcher.bootstrap.EntryPoint.run([Ljava/lang/String;)V+12
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.11
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.11
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.11
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.11
j  org.gradle.launcher.bootstrap.ProcessBootstrap.runNoExit(Ljava/lang/String;[Ljava/lang/String;)V+159
j  org.gradle.launcher.bootstrap.ProcessBootstrap.run(Ljava/lang/String;[Ljava/lang/String;)V+2
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.11
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.11
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.11
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.11
j  org.gradle.launcher.GradleMain.main([Ljava/lang/String;)V+100
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.11
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.11
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.11
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.11
j  org.gradle.wrapper.BootstrapMainStarter.start([Ljava/lang/String;Ljava/io/File;)V+105
j  org.gradle.wrapper.WrapperExecutor.execute([Ljava/lang/String;Lorg/gradle/wrapper/Install;Lorg/gradle/wrapper/BootstrapMainStarter;)V+14
j  org.gradle.wrapper.GradleWrapperMain.main([Ljava/lang/String;)V+206
v  ~StubRoutines::call_stub

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000016c57bd0008


Registers:
RAX=0x0000000000000001, RBX=0x0000016c9c5d5420, RCX=0x0000000000000000, RDX=0x0000000000000000
RSP=0x0000005c835fc550, RBP=0x0000000000112fe6, RSI=0x0000016c9c5d5420, RDI=0x0000016c573380d0
R8 =0x0000000000000001, R9 =0x00000000000000c5, R10=0x0000000000000000, R11=0x0000005c835fc4f0
R12=0x0000005c835fc5d0, R13=0x0000016c56b087d0, R14=0x000000000005baa2, R15=0x0000000000000736
RIP=0x00007fff5db457e0, EFLAGS=0x0000000000010287


Register to memory mapping:

RIP=0x00007fff5db457e0 net.dll
RAX=0x0000000000000001 is an unknown value
RBX=0x0000016c9c5d5420 points into unknown readable memory: 0x12ff024a00000002 | 02 00 00 00 4a 02 ff 12
RCX=0x0 is NULL
RDX=0x0 is NULL
RSP=0x0000005c835fc550 is pointing into the stack for thread: 0x0000016c3cd25260
RBP=0x0000000000112fe6 is an unknown value
RSI=0x0000016c9c5d5420 points into unknown readable memory: 0x12ff024a00000002 | 02 00 00 00 4a 02 ff 12
RDI={method} {0x0000016c573380d8} 'getNetworkInterfaces' '()Ljava/util/Enumeration;' in 'java/net/NetworkInterface'
R8 =0x0000000000000001 is an unknown value
R9 =0x00000000000000c5 is an unknown value
R10=0x0 is NULL
R11=0x0000005c835fc4f0 is pointing into the stack for thread: 0x0000016c3cd25260
R12=0x0000005c835fc5d0 is pointing into the stack for thread: 0x0000016c3cd25260
R13=0x0000016c56b087d0 points into unknown readable memory: 0x0000016c9c614820 | 20 48 61 9c 6c 01 00 00
R14=0x000000000005baa2 is an unknown value
R15=0x0000000000000736 is an unknown value


Top of Stack: (sp=0x0000005c835fc550)
0x0000005c835fc550:   0000016c9c5d5fb0 0000000000000029
0x0000005c835fc560:   0000016c3cd25510 00007fff5db4609b
0x0000005c835fc570:   0000005c835fc700 0000005c835fc7b0
0x0000005c835fc580:   0000000000000000 0000000000000029
0x0000005c835fc590:   0000016c56b087d0 00007fff5db46e80
0x0000005c835fc5a0:   0000016c3cd25510 0000016c3cd25510
0x0000005c835fc5b0:   0000005c835fc649 0000016c56b087d0
0x0000005c835fc5c0:   0000016c00000000 0000000000000000
0x0000005c835fc5d0:   0000016c3cd25260 0000016c573380d0
0x0000005c835fc5e0:   0000016c9c6f6ac0 fffffffe00000000
0x0000005c835fc5f0:   0000000000000000 0000000000000000
0x0000005c835fc600:   0000016c3cd25260 00007fff3121ba23
0x0000005c835fc610:   0000005c835fc878 0000016c3cd25260
0x0000005c835fc620:   00000000fe83b1a0 00000000000000b8
0x0000005c835fc630:   0000005c835fc740 00007fff30f5030d
0x0000005c835fc640:   0000016c573380d0 0000016c9c7fd630 

Instructions: (pc=0x00007fff5db457e0)
0x00007fff5db456e0:   ff ff 48 8b cb 48 8b 5b 40 ff 15 c1 9b 00 00 48
0x00007fff5db456f0:   8b cb 48 85 db 75 ee e9 bf fb ff ff 48 8b cb 48
0x00007fff5db45700:   8b 5b 40 ff 15 a7 9b 00 00 48 8b cb 48 85 db 75
0x00007fff5db45710:   ee e9 a5 fb ff ff 48 8b cb 48 8b 5b 40 ff 15 8d
0x00007fff5db45720:   9b 00 00 48 8b cb 48 85 db 75 ee e9 8b fb ff ff
0x00007fff5db45730:   48 89 5c 24 08 48 89 74 24 10 57 48 83 ec 20 48
0x00007fff5db45740:   8b f2 49 8b f8 48 8d 54 24 48 48 8b d9 e8 92 08
0x00007fff5db45750:   00 00 85 c0 79 04 33 c0 eb 22 4c 8b 4c 24 48 4c
0x00007fff5db45760:   8b c7 48 8b d6 48 8b cb e8 1f 00 00 00 48 8b 4c
0x00007fff5db45770:   24 48 8b d8 ff 15 36 9b 00 00 8b c3 48 8b 5c 24
0x00007fff5db45780:   30 48 8b 74 24 38 48 83 c4 20 5f c3 48 8b c4 48
0x00007fff5db45790:   89 58 10 48 89 68 18 48 89 70 20 48 89 48 08 57
0x00007fff5db457a0:   41 54 41 55 41 56 41 57 48 83 ec 20 4c 8b ea 49
0x00007fff5db457b0:   8b f9 33 d2 4d 8b e0 4d 85 c9 75 07 33 c0 e9 fc
0x00007fff5db457c0:   00 00 00 44 8b fa 48 8b da 44 8b f2 41 39 11 0f
0x00007fff5db457d0:   86 e3 00 00 00 41 8b c6 48 8d 2c 40 41 8b 45 10
0x00007fff5db457e0:   39 44 ef 08 0f 85 bd 00 00 00 39 54 ef 04 0f 84
0x00007fff5db457f0:   b3 00 00 00 b9 48 00 00 00 ff 15 b9 9a 00 00 33
0x00007fff5db45800:   d2 48 8b f0 48 85 c0 0f 84 cf 00 00 00 44 8d 42
0x00007fff5db45810:   02 66 44 89 00 8b 44 ef 04 89 46 04 41 8b 4d 14
0x00007fff5db45820:   83 e9 06 74 23 83 e9 03 74 1e 83 e9 06 74 19 83
0x00007fff5db45830:   e9 08 74 0f 83 e9 01 74 0f 83 e9 04 74 05 83 f9
0x00007fff5db45840:   2b 74 05 83 c8 ff eb 4a 66 44 89 46 1c ba 01 00
0x00007fff5db45850:   00 00 8b 4c ef 04 8b 44 ef 0c 23 c8 39 54 ef 10
0x00007fff5db45860:   75 06 f7 d0 0b c1 eb 02 8b c1 89 46 20 8b 4c ef
0x00007fff5db45870:   0c ff 15 89 99 00 00 33 d2 8b c8 66 89 56 38 85
0x00007fff5db45880:   c0 74 13 0f b7 c2 44 8d 42 01 66 41 03 c0 03 c9
0x00007fff5db45890:   75 f8 66 89 46 38 b8 01 00 00 00 48 89 5e 40 44
0x00007fff5db458a0:   03 f8 48 8b de eb 05 b8 01 00 00 00 44 03 f0 44
0x00007fff5db458b0:   3b 37 0f 82 1d ff ff ff 49 89 1c 24 41 8b c7 48
0x00007fff5db458c0:   8b 5c 24 58 48 8b 6c 24 60 48 8b 74 24 68 48 83
0x00007fff5db458d0:   c4 20 41 5f 41 5e 41 5d 41 5c 5f c3 48 8b 4c 24 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0000016c9c5d5fb0 points into unknown readable memory: 0x0002000600000002 | 02 00 00 00 06 00 02 00
stack at sp + 1 slots: 0x0000000000000029 is an unknown value
stack at sp + 2 slots: 0x0000016c3cd25510 points into unknown readable memory: 0x00007fff31673670 | 70 36 67 31 ff 7f 00 00
stack at sp + 3 slots: 0x00007fff5db4609b net.dll
stack at sp + 4 slots: 0x0000005c835fc700 is pointing into the stack for thread: 0x0000016c3cd25260
stack at sp + 5 slots: 0x0000005c835fc7b0 is pointing into the stack for thread: 0x0000016c3cd25260
stack at sp + 6 slots: 0x0 is NULL
stack at sp + 7 slots: 0x0000000000000029 is an unknown value


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016c9c55dfc0, length=13, elements={
0x0000016c3cd25260, 0x0000016c3cdd8c10, 0x0000016c3cdda340, 0x0000016c569013d0,
0x0000016c56904db0, 0x0000016c569062e0, 0x0000016c56906ca0, 0x0000016c569085b0,
0x0000016c5690e7f0, 0x0000016c5690f1f0, 0x0000016c56972600, 0x0000016c569812d0,
0x0000016c9c584c70
}

Java Threads: ( => current thread )
=>0x0000016c3cd25260 JavaThread "main" [_thread_in_native, id=52684, stack(0x0000005c83500000,0x0000005c83600000)]
  0x0000016c3cdd8c10 JavaThread "Reference Handler" daemon [_thread_blocked, id=34168, stack(0x0000005c83c00000,0x0000005c83d00000)]
  0x0000016c3cdda340 JavaThread "Finalizer" daemon [_thread_blocked, id=41132, stack(0x0000005c83d00000,0x0000005c83e00000)]
  0x0000016c569013d0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=29756, stack(0x0000005c83e00000,0x0000005c83f00000)]
  0x0000016c56904db0 JavaThread "Attach Listener" daemon [_thread_blocked, id=25552, stack(0x0000005c83f00000,0x0000005c84000000)]
  0x0000016c569062e0 JavaThread "Service Thread" daemon [_thread_blocked, id=5328, stack(0x0000005c84000000,0x0000005c84100000)]
  0x0000016c56906ca0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=35760, stack(0x0000005c84100000,0x0000005c84200000)]
  0x0000016c569085b0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=21936, stack(0x0000005c84200000,0x0000005c84300000)]
  0x0000016c5690e7f0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=48168, stack(0x0000005c84300000,0x0000005c84400000)]
  0x0000016c5690f1f0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=18504, stack(0x0000005c84400000,0x0000005c84500000)]
  0x0000016c56972600 JavaThread "Notification Thread" daemon [_thread_blocked, id=34576, stack(0x0000005c84500000,0x0000005c84600000)]
  0x0000016c569812d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=8172, stack(0x0000005c84700000,0x0000005c84800000)]
  0x0000016c9c584c70 JavaThread "pool-1-thread-1" [_thread_blocked, id=54936, stack(0x0000005c84c00000,0x0000005c84d00000)]

Other Threads:
  0x0000016c3cdd0c00 VMThread "VM Thread" [stack: 0x0000005c83b00000,0x0000005c83c00000] [id=42276]
  0x0000016c56974a50 WatcherThread [stack: 0x0000005c84600000,0x0000005c84700000] [id=2528]
  0x0000016c3cd44f80 GCTaskThread "GC Thread#0" [stack: 0x0000005c83600000,0x0000005c83700000] [id=53060]
  0x0000016c56cf5650 GCTaskThread "GC Thread#1" [stack: 0x0000005c84800000,0x0000005c84900000] [id=54688]
  0x0000016c56c239c0 GCTaskThread "GC Thread#2" [stack: 0x0000005c84900000,0x0000005c84a00000] [id=54768]
  0x0000016c9c6fa1f0 GCTaskThread "GC Thread#3" [stack: 0x0000005c84a00000,0x0000005c84b00000] [id=54772]
  0x0000016c9c6fa4b0 GCTaskThread "GC Thread#4" [stack: 0x0000005c84b00000,0x0000005c84c00000] [id=54776]
  0x0000016c3cd476e0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000005c83700000,0x0000005c83800000] [id=53064]
  0x0000016c3cd480f0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000005c83800000,0x0000005c83900000] [id=53068]
  0x0000016c3cdad950 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000005c83900000,0x0000005c83a00000] [id=53072]
  0x0000016c3cdb0060 ConcurrentGCThread "G1 Service" [stack: 0x0000005c83a00000,0x0000005c83b00000] [id=53076]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000fc000000, size: 64 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000016c57000000-0x0000016c57bd0000-0x0000016c57bd0000), size 12386304, SharedBaseAddress: 0x0000016c57000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016c58000000-0x0000016c98000000, reserved size: 1073741824
Narrow klass base: 0x0000016c57000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16228M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 64M
 Heap Initial Capacity: 64M
 Heap Max Capacity: 64M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 65536K, used 20640K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 3 survivors (3072K)
 Metaspace       used 6571K, committed 6784K, reserved 1114112K
  class space    used 912K, committed 1024K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000fc000000, 0x00000000fc100000, 0x00000000fc100000|100%|HS|  |TAMS 0x00000000fc000000, 0x00000000fc000000| Complete 
|   1|0x00000000fc100000, 0x00000000fc200000, 0x00000000fc200000|100%|HC|  |TAMS 0x00000000fc100000, 0x00000000fc100000| Complete 
|   2|0x00000000fc200000, 0x00000000fc300000, 0x00000000fc300000|100%|HC|  |TAMS 0x00000000fc200000, 0x00000000fc200000| Complete 
|   3|0x00000000fc300000, 0x00000000fc400000, 0x00000000fc400000|100%|HC|  |TAMS 0x00000000fc300000, 0x00000000fc300000| Complete 
|   4|0x00000000fc400000, 0x00000000fc500000, 0x00000000fc500000|100%| O|  |TAMS 0x00000000fc400000, 0x00000000fc400000| Untracked 
|   5|0x00000000fc500000, 0x00000000fc600000, 0x00000000fc600000|100%| O|  |TAMS 0x00000000fc500000, 0x00000000fc500000| Untracked 
|   6|0x00000000fc600000, 0x00000000fc700000, 0x00000000fc700000|100%| O|  |TAMS 0x00000000fc600000, 0x00000000fc600000| Untracked 
|   7|0x00000000fc700000, 0x00000000fc728000, 0x00000000fc800000| 15%| O|  |TAMS 0x00000000fc700000, 0x00000000fc700000| Untracked 
|   8|0x00000000fc800000, 0x00000000fc800000, 0x00000000fc900000|  0%| F|  |TAMS 0x00000000fc800000, 0x00000000fc800000| Untracked 
|   9|0x00000000fc900000, 0x00000000fc900000, 0x00000000fca00000|  0%| F|  |TAMS 0x00000000fc900000, 0x00000000fc900000| Untracked 
|  10|0x00000000fca00000, 0x00000000fca00000, 0x00000000fcb00000|  0%| F|  |TAMS 0x00000000fca00000, 0x00000000fca00000| Untracked 
|  11|0x00000000fcb00000, 0x00000000fcb00000, 0x00000000fcc00000|  0%| F|  |TAMS 0x00000000fcb00000, 0x00000000fcb00000| Untracked 
|  12|0x00000000fcc00000, 0x00000000fcc00000, 0x00000000fcd00000|  0%| F|  |TAMS 0x00000000fcc00000, 0x00000000fcc00000| Untracked 
|  13|0x00000000fcd00000, 0x00000000fcd00000, 0x00000000fce00000|  0%| F|  |TAMS 0x00000000fcd00000, 0x00000000fcd00000| Untracked 
|  14|0x00000000fce00000, 0x00000000fce00000, 0x00000000fcf00000|  0%| F|  |TAMS 0x00000000fce00000, 0x00000000fce00000| Untracked 
|  15|0x00000000fcf00000, 0x00000000fcf00000, 0x00000000fd000000|  0%| F|  |TAMS 0x00000000fcf00000, 0x00000000fcf00000| Untracked 
|  16|0x00000000fd000000, 0x00000000fd000000, 0x00000000fd100000|  0%| F|  |TAMS 0x00000000fd000000, 0x00000000fd000000| Untracked 
|  17|0x00000000fd100000, 0x00000000fd100000, 0x00000000fd200000|  0%| F|  |TAMS 0x00000000fd100000, 0x00000000fd100000| Untracked 
|  18|0x00000000fd200000, 0x00000000fd200000, 0x00000000fd300000|  0%| F|  |TAMS 0x00000000fd200000, 0x00000000fd200000| Untracked 
|  19|0x00000000fd300000, 0x00000000fd300000, 0x00000000fd400000|  0%| F|  |TAMS 0x00000000fd300000, 0x00000000fd300000| Untracked 
|  20|0x00000000fd400000, 0x00000000fd400000, 0x00000000fd500000|  0%| F|  |TAMS 0x00000000fd400000, 0x00000000fd400000| Untracked 
|  21|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000, 0x00000000fd500000| Untracked 
|  22|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000, 0x00000000fd600000| Untracked 
|  23|0x00000000fd700000, 0x00000000fd700000, 0x00000000fd800000|  0%| F|  |TAMS 0x00000000fd700000, 0x00000000fd700000| Untracked 
|  24|0x00000000fd800000, 0x00000000fd800000, 0x00000000fd900000|  0%| F|  |TAMS 0x00000000fd800000, 0x00000000fd800000| Untracked 
|  25|0x00000000fd900000, 0x00000000fd900000, 0x00000000fda00000|  0%| F|  |TAMS 0x00000000fd900000, 0x00000000fd900000| Untracked 
|  26|0x00000000fda00000, 0x00000000fda00000, 0x00000000fdb00000|  0%| F|  |TAMS 0x00000000fda00000, 0x00000000fda00000| Untracked 
|  27|0x00000000fdb00000, 0x00000000fdb00000, 0x00000000fdc00000|  0%| F|  |TAMS 0x00000000fdb00000, 0x00000000fdb00000| Untracked 
|  28|0x00000000fdc00000, 0x00000000fdc00000, 0x00000000fdd00000|  0%| F|  |TAMS 0x00000000fdc00000, 0x00000000fdc00000| Untracked 
|  29|0x00000000fdd00000, 0x00000000fdd00000, 0x00000000fde00000|  0%| F|  |TAMS 0x00000000fdd00000, 0x00000000fdd00000| Untracked 
|  30|0x00000000fde00000, 0x00000000fde00000, 0x00000000fdf00000|  0%| F|  |TAMS 0x00000000fde00000, 0x00000000fde00000| Untracked 
|  31|0x00000000fdf00000, 0x00000000fdf00000, 0x00000000fe000000|  0%| F|  |TAMS 0x00000000fdf00000, 0x00000000fdf00000| Untracked 
|  32|0x00000000fe000000, 0x00000000fe000000, 0x00000000fe100000|  0%| F|  |TAMS 0x00000000fe000000, 0x00000000fe000000| Untracked 
|  33|0x00000000fe100000, 0x00000000fe100000, 0x00000000fe200000|  0%| F|  |TAMS 0x00000000fe100000, 0x00000000fe100000| Untracked 
|  34|0x00000000fe200000, 0x00000000fe200000, 0x00000000fe300000|  0%| F|  |TAMS 0x00000000fe200000, 0x00000000fe200000| Untracked 
|  35|0x00000000fe300000, 0x00000000fe300000, 0x00000000fe400000|  0%| F|  |TAMS 0x00000000fe300000, 0x00000000fe300000| Untracked 
|  36|0x00000000fe400000, 0x00000000fe400000, 0x00000000fe500000|  0%| F|  |TAMS 0x00000000fe400000, 0x00000000fe400000| Untracked 
|  37|0x00000000fe500000, 0x00000000fe500000, 0x00000000fe600000|  0%| F|  |TAMS 0x00000000fe500000, 0x00000000fe500000| Untracked 
|  38|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| S|CS|TAMS 0x00000000fe600000, 0x00000000fe600000| Complete 
|  39|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| S|CS|TAMS 0x00000000fe700000, 0x00000000fe700000| Complete 
|  40|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| S|CS|TAMS 0x00000000fe800000, 0x00000000fe800000| Complete 
|  41|0x00000000fe900000, 0x00000000fe900000, 0x00000000fea00000|  0%| F|  |TAMS 0x00000000fe900000, 0x00000000fe900000| Untracked 
|  42|0x00000000fea00000, 0x00000000fea00000, 0x00000000feb00000|  0%| F|  |TAMS 0x00000000fea00000, 0x00000000fea00000| Untracked 
|  43|0x00000000feb00000, 0x00000000feb00000, 0x00000000fec00000|  0%| F|  |TAMS 0x00000000feb00000, 0x00000000feb00000| Untracked 
|  44|0x00000000fec00000, 0x00000000fec00000, 0x00000000fed00000|  0%| F|  |TAMS 0x00000000fec00000, 0x00000000fec00000| Untracked 
|  45|0x00000000fed00000, 0x00000000fed00000, 0x00000000fee00000|  0%| F|  |TAMS 0x00000000fed00000, 0x00000000fed00000| Untracked 
|  46|0x00000000fee00000, 0x00000000fee00000, 0x00000000fef00000|  0%| F|  |TAMS 0x00000000fee00000, 0x00000000fee00000| Untracked 
|  47|0x00000000fef00000, 0x00000000fef00000, 0x00000000ff000000|  0%| F|  |TAMS 0x00000000fef00000, 0x00000000fef00000| Untracked 
|  48|0x00000000ff000000, 0x00000000ff000000, 0x00000000ff100000|  0%| F|  |TAMS 0x00000000ff000000, 0x00000000ff000000| Untracked 
|  49|0x00000000ff100000, 0x00000000ff100000, 0x00000000ff200000|  0%| F|  |TAMS 0x00000000ff100000, 0x00000000ff100000| Untracked 
|  50|0x00000000ff200000, 0x00000000ff200000, 0x00000000ff300000|  0%| F|  |TAMS 0x00000000ff200000, 0x00000000ff200000| Untracked 
|  51|0x00000000ff300000, 0x00000000ff300000, 0x00000000ff400000|  0%| F|  |TAMS 0x00000000ff300000, 0x00000000ff300000| Untracked 
|  52|0x00000000ff400000, 0x00000000ff400000, 0x00000000ff500000|  0%| F|  |TAMS 0x00000000ff400000, 0x00000000ff400000| Untracked 
|  53|0x00000000ff500000, 0x00000000ff571250, 0x00000000ff600000| 44%| E|  |TAMS 0x00000000ff500000, 0x00000000ff500000| Complete 
|  54|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000, 0x00000000ff600000| Complete 
|  55|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000, 0x00000000ff700000| Complete 
|  56|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000, 0x00000000ff800000| Complete 
|  57|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000, 0x00000000ff900000| Complete 
|  58|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000, 0x00000000ffa00000| Complete 
|  59|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000, 0x00000000ffb00000| Complete 
|  60|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000, 0x00000000ffc00000| Complete 
|  61|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000, 0x00000000ffd00000| Complete 
|  62|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000, 0x00000000ffe00000| Complete 
|  63|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000, 0x00000000fff00000| Complete 

Card table byte_map: [0x0000016c3ac50000,0x0000016c3ac70000] _byte_map_base: 0x0000016c3a470000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000016c3cd455a0, (CMBitMap*) 0x0000016c3cd455e0
 Prev Bits: [0x0000016c53b30000, 0x0000016c53c30000)
 Next Bits: [0x0000016c53c30000, 0x0000016c53d30000)

Polling page: 0x0000016c3ac00000

Metaspace:

Usage:
  Non-class:      5.53 MB used.
      Class:    912.66 KB used.
       Both:      6.42 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       5.62 MB (  9%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.00 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       6.62 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  9.04 MB
       Class:  15.04 MB
        Both:  24.08 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 70.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 106.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 241.
num_chunk_merges: 0.
num_chunk_splits: 162.
num_chunks_enlarged: 140.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=458Kb max_used=458Kb free=119542Kb
 bounds [0x0000016c4c0b0000, 0x0000016c4c320000, 0x0000016c535e0000]
CodeHeap 'profiled nmethods': size=120000Kb used=2164Kb max_used=2164Kb free=117836Kb
 bounds [0x0000016c445e0000, 0x0000016c44850000, 0x0000016c4bb10000]
CodeHeap 'non-nmethods': size=5760Kb used=1147Kb max_used=1167Kb free=4612Kb
 bounds [0x0000016c4bb10000, 0x0000016c4bd80000, 0x0000016c4c0b0000]
 total_blobs=1553 nmethods=1117 adapters=349
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 25.690 Thread 0x0000016c5690e7f0 1109       3       java.lang.String::encode (48 bytes)
Event: 25.690 Thread 0x0000016c5690e7f0 nmethod 1109 0x0000016c447edb90 code [0x0000016c447edd80, 0x0000016c447ee0d8]
Event: 25.690 Thread 0x0000016c5690e7f0 1110   !   3       java.lang.String::encodeWithEncoder (307 bytes)
Event: 25.693 Thread 0x0000016c5690e7f0 nmethod 1110 0x0000016c447ee210 code [0x0000016c447ee5e0, 0x0000016c447f0088]
Event: 25.693 Thread 0x0000016c5690e7f0 1115       3       java.util.Random::nextBoolean (14 bytes)
Event: 25.693 Thread 0x0000016c5690e7f0 nmethod 1115 0x0000016c447f0a10 code [0x0000016c447f0bc0, 0x0000016c447f0de8]
Event: 25.693 Thread 0x0000016c5690e7f0 1116       3       java.util.Random::next (47 bytes)
Event: 25.695 Thread 0x0000016c5690e7f0 nmethod 1116 0x0000016c447f0e90 code [0x0000016c447f1040, 0x0000016c447f12c8]
Event: 25.695 Thread 0x0000016c5690e7f0 1117       3       java.util.concurrent.atomic.AtomicLong::compareAndSet (13 bytes)
Event: 25.696 Thread 0x0000016c5690e7f0 nmethod 1117 0x0000016c447f1410 code [0x0000016c447f15a0, 0x0000016c447f16d8]
Event: 25.728 Thread 0x0000016c5690e7f0 1119       3       jdk.internal.org.objectweb.asm.ByteVector::enlarge (51 bytes)
Event: 25.728 Thread 0x0000016c5690e7f0 nmethod 1119 0x0000016c447f1790 code [0x0000016c447f1940, 0x0000016c447f1c68]
Event: 25.728 Thread 0x0000016c5690e7f0 1120       3       java.lang.invoke.InvokerBytecodeGenerator::emitImplicitConversion (161 bytes)
Event: 25.730 Thread 0x0000016c5690e7f0 nmethod 1120 0x0000016c447f1d90 code [0x0000016c447f20a0, 0x0000016c447f2f38]
Event: 25.923 Thread 0x0000016c569085b0 1122       4       jdk.internal.misc.Unsafe::getIntUnaligned (12 bytes)
Event: 25.923 Thread 0x0000016c5690e7f0 1123       3       sun.security.provider.ByteArrayAccess::b2iBig64 (231 bytes)
Event: 25.923 Thread 0x0000016c569085b0 nmethod 1122 0x0000016c4c121e10 code [0x0000016c4c121f80, 0x0000016c4c122038]
Event: 25.928 Thread 0x0000016c569085b0 1124       4       java.util.concurrent.ConcurrentHashMap::get (162 bytes)
Event: 25.931 Thread 0x0000016c5690e7f0 nmethod 1123 0x0000016c447f3410 code [0x0000016c447f3f80, 0x0000016c447fad78]
Event: 25.932 Thread 0x0000016c569085b0 nmethod 1124 0x0000016c4c122110 code [0x0000016c4c1222a0, 0x0000016c4c122598]

GC Heap History (2 events):
Event: 20.445 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 65536K, used 26624K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 3834K, committed 4032K, reserved 1114112K
  class space    used 491K, committed 576K, reserved 1048576K
}
Event: 20.783 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 65536K, used 10400K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 3834K, committed 4032K, reserved 1114112K
  class space    used 491K, committed 576K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c4c114300 sp=0x0000005c835fa6c0
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb623a3 sp=0x0000005c835fa660 mode 2
Event: 23.643 Thread 0x0000016c3cd25260 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000016c4c114300 relative=0x00000000000005a0
Event: 23.643 Thread 0x0000016c3cd25260 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000016c4c114300 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 149 c2
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c4c114300 sp=0x0000005c835fa6c0
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb623a3 sp=0x0000005c835fa660 mode 2
Event: 23.643 Thread 0x0000016c3cd25260 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000016c4c114300 relative=0x00000000000005a0
Event: 23.643 Thread 0x0000016c3cd25260 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000016c4c114300 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 149 c2
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c4c114300 sp=0x0000005c835fa6c0
Event: 23.643 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb623a3 sp=0x0000005c835fa660 mode 2
Event: 23.781 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c446d3a38 sp=0x0000005c835fa310
Event: 23.781 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb62b43 sp=0x0000005c835f9828 mode 0
Event: 24.225 Thread 0x0000016c3cd25260 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000016c4c114300 relative=0x00000000000005a0
Event: 24.225 Thread 0x0000016c3cd25260 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000016c4c114300 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 149 c2
Event: 24.225 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c4c114300 sp=0x0000005c835fbae0
Event: 24.225 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb623a3 sp=0x0000005c835fba80 mode 2
Event: 25.388 Thread 0x0000016c3cd25260 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016c4c1093f0 relative=0x0000000000001c30
Event: 25.388 Thread 0x0000016c3cd25260 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016c4c1093f0 method=java.io.WinNTFileSystem.normalize(Ljava/lang/String;)Ljava/lang/String; @ 128 c2
Event: 25.388 Thread 0x0000016c3cd25260 DEOPT PACKING pc=0x0000016c4c1093f0 sp=0x0000005c835fbaf0
Event: 25.388 Thread 0x0000016c3cd25260 DEOPT UNPACKING pc=0x0000016c4bb623a3 sp=0x0000005c835fbad8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (16 events):
Event: 6.146 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffcc1bc0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000ffcc1bc0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 18.322 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000febdc6d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000febdc6d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 19.400 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000fea61948}: org/slf4j/impl/StaticMarkerBinder> (0x00000000fea61948) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 245]
Event: 19.548 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000feafd5f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000feafd5f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 19.569 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe90f0a0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000fe90f0a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 19.574 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe918170}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000fe918170) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 19.747 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe923e60}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000fe923e60) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 19.880 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe927fa8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000fe927fa8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 20.118 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe93b0f0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000fe93b0f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 21.977 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffca5900}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ffca5900) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 22.242 Thread 0x0000016c3cd25260 Implicit null exception at 0x0000016c4c0f8b8f to 0x0000016c4c0f9104
Event: 22.242 Thread 0x0000016c3cd25260 Implicit null exception at 0x0000016c4c0eb18f to 0x0000016c4c0eb6f8
Event: 23.125 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffa44280}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000000ffa44280) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.905 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ff653b78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000000ff653b78) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 25.729 Thread 0x0000016c3cd25260 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ff52d358}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000ff52d358) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 26.097 Thread 0x0000016c3cd25260 Exception <a 'java/lang/Error'{0x00000000ff5505d0}: IP Helper Library GetIpAddrTable function failed> (0x00000000ff5505d0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 535]

VM Operations (20 events):
Event: 19.016 Executing VM operation: Cleanup
Event: 19.037 Executing VM operation: Cleanup done
Event: 19.057 Executing VM operation: HandshakeAllThreads
Event: 19.057 Executing VM operation: HandshakeAllThreads done
Event: 19.171 Executing VM operation: HandshakeAllThreads
Event: 19.171 Executing VM operation: HandshakeAllThreads done
Event: 19.570 Executing VM operation: HandshakeAllThreads
Event: 19.570 Executing VM operation: HandshakeAllThreads done
Event: 20.445 Executing VM operation: G1CollectForAllocation
Event: 20.784 Executing VM operation: G1CollectForAllocation done
Event: 21.789 Executing VM operation: Cleanup
Event: 21.970 Executing VM operation: Cleanup done
Event: 22.977 Executing VM operation: Cleanup
Event: 23.118 Executing VM operation: Cleanup done
Event: 23.690 Executing VM operation: HandshakeAllThreads
Event: 23.690 Executing VM operation: HandshakeAllThreads done
Event: 24.704 Executing VM operation: Cleanup
Event: 24.704 Executing VM operation: Cleanup done
Event: 25.674 Executing VM operation: HandshakeAllThreads
Event: 25.674 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 25.674 loading class sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
Event: 25.674 loading class sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator done
Event: 25.674 loading class sun/nio/fs/WindowsPath$WindowsPathWithAttributes
Event: 25.674 loading class sun/nio/fs/BasicFileAttributesHolder
Event: 25.674 loading class sun/nio/fs/BasicFileAttributesHolder done
Event: 25.674 loading class sun/nio/fs/WindowsPath$WindowsPathWithAttributes done
Event: 25.920 loading class sun/security/provider/AbstractDrbg$NonceProvider
Event: 25.921 loading class sun/security/provider/AbstractDrbg$NonceProvider done
Event: 25.921 loading class sun/security/provider/SHA2$SHA256
Event: 25.922 loading class sun/security/provider/SHA2
Event: 25.922 loading class sun/security/provider/SHA2 done
Event: 25.922 loading class sun/security/provider/SHA2$SHA256 done
Event: 25.928 loading class java/net/DatagramSocket
Event: 25.928 loading class java/net/DatagramSocket done
Event: 25.928 loading class java/net/DatagramSocket$1
Event: 25.928 loading class java/net/DatagramSocket$1 done
Event: 25.929 loading class sun/net/NetProperties
Event: 25.929 loading class sun/net/NetProperties done
Event: 25.929 loading class sun/net/NetProperties$1
Event: 25.929 loading class sun/net/NetProperties$1 done


Dynamic libraries:
0x00007ff6208b0000 - 0x00007ff6208c0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007fffa6c00000 - 0x00007fffa6e65000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fffa49a0000 - 0x00007fffa4a69000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fffa40e0000 - 0x00007fffa44c8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fffa3f90000 - 0x00007fffa40db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff9cb70000 - 0x00007fff9cb89000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007fffa6b00000 - 0x00007fffa6bb3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fffa6070000 - 0x00007fffa6119000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff9c920000 - 0x00007fff9c93b000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007fffa6a00000 - 0x00007fffa6aa6000 	C:\WINDOWS\System32\sechost.dll
0x00007fffa57f0000 - 0x00007fffa5905000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fffa5350000 - 0x00007fffa551a000 	C:\WINDOWS\System32\USER32.dll
0x00007fffa3ed0000 - 0x00007fffa3ef7000 	C:\WINDOWS\System32\win32u.dll
0x00007fffa4d70000 - 0x00007fffa4d9b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fffa4580000 - 0x00007fffa46b7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fffa44d0000 - 0x00007fffa4573000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff8ec00000 - 0x00007fff8ee9a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff9be00000 - 0x00007fff9be0b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fffa4da0000 - 0x00007fffa4dd0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff9e700000 - 0x00007fff9e70c000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007fff68070000 - 0x00007fff680fe000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007fff30bd0000 - 0x00007fff317b0000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007fffa4a70000 - 0x00007fffa4a78000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff89d80000 - 0x00007fff89d8a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fffa6120000 - 0x00007fffa6194000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff97df0000 - 0x00007fff97e25000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fffa2c10000 - 0x00007fffa2c2b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff9c910000 - 0x00007fff9c91a000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007fffa1570000 - 0x00007fffa17b1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fffa4fc0000 - 0x00007fffa5345000 	C:\WINDOWS\System32\combase.dll
0x00007fffa55c0000 - 0x00007fffa56a1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff8da10000 - 0x00007fff8da49000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fffa4780000 - 0x00007fffa4819000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff5db60000 - 0x00007fff5db85000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ffebf6a0000 - 0x00007ffebf777000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007fffa61a0000 - 0x00007fffa68e2000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fffa4820000 - 0x00007fffa4994000 	C:\WINDOWS\System32\wintypes.dll
0x00007fffa1970000 - 0x00007fffa21c8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fffa4a80000 - 0x00007fffa4b71000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fffa5da0000 - 0x00007fffa5e0a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fffa3c60000 - 0x00007fffa3c8f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff5db40000 - 0x00007fff5db59000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007fff9c0f0000 - 0x00007fff9c20e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fffa31a0000 - 0x00007fffa320a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff3d6e0000 - 0x00007fff3d6f6000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007fff30bb0000 - 0x00007fff30bc8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007fff30ba0000 - 0x00007fff30bb0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007fff90860000 - 0x00007fff90887000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007fff68060000 - 0x00007fff6806a000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007fff30b90000 - 0x00007fff30b9b000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007fffa3450000 - 0x00007fffa346b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fffa2b70000 - 0x00007fffa2baa000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fffa3240000 - 0x00007fffa326b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fffa3c30000 - 0x00007fffa3c56000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fffa3470000 - 0x00007fffa347c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fffa25d0000 - 0x00007fffa2603000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fffa68f0000 - 0x00007fffa68fa000 	C:\WINDOWS\System32\NSI.dll
0x00007fff9c660000 - 0x00007fff9c67f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff9c410000 - 0x00007fff9c435000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fffa2670000 - 0x00007fffa2797000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64

VM Arguments:
jvm_args: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew 
java_command: org.gradle.wrapper.GradleWrapperMain tasks
java_class_path (initial): C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\\gradle\wrapper\gradle-wrapper.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 67108864                                  {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 67108864                                  {product} {command line}
   size_t MaxNewSize                               = 39845888                                  {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 67108864                                  {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 67108864                               {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
CLASSPATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\sources\mobile-e-luxe-1.0\node_modules\.bin;C:\Users\<USER>\sources\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v22.15.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;%JAVA_HOME%\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\wamp64\bin\php\php8.1.13;C:\ProgramData\ComposerSetup\bin;C:\Program Files\RabbitMQ Server\rabbitmq_server-3.12.2\sbin;C:\Program Files\MongoDB\Tools\100\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\cloudflared\;c:\users\<USER>\pear.bat;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Progr
USERNAME=MSI
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 1 days 5:39 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16228M (15M free)
TotalPageFile size 62338M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 77M, peak: 77M
current process commit charge ("private bytes"): 165M, peak: 165M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.11+7-LTS-207) for windows-amd64 JRE (17.0.11+7-LTS-207), built on Mar 11 2024 19:01:50 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
