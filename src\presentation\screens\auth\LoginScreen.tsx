import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
// import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { loginAsync, clearError } from '../../store/slices/authSlice';
import { AuthStackParamList } from '../../navigation/AppNavigator';
import { selectLogo, selectGlobalSetting, fetchGlobalSettingsAsync, fetchStoreCustomizationSettingsAsync } from '../../store/slices/settingsSlice';
import { ELuxeColors } from '../../../theme/colors';
import { TextStyles, FontWeights } from '../../../theme/typography';

type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Login'>;

const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const dispatch = useAppDispatch();

  // Utilisation sécurisée de useTranslation avec fallback
  let t: (key: string) => string;
  try {
    const translation = useTranslation();
    t = translation.t;
  } catch (error) {
    console.warn('i18n not ready, using fallback translations');
    // Fallback simple
    t = (key: string) => {
      const fallbacks: Record<string, string> = {
        'Auth.welcomeBack': 'Welcome Back',
        'Auth.signInToAccount': 'Sign in to your account',
        'Auth.enterEmail': 'Enter your email',
        'Auth.enterPassword': 'Enter your password',
        'Auth.rememberMe': 'Remember Me',
        'Auth.forgotPassword': 'Forgot Password?',
        'Auth.signIn': 'Sign In',
        'Auth.dontHaveAccount': "Don't have an account?",
        'Auth.signUp': 'Sign Up',
      };
      return fallbacks[key] || key;
    };
  }

  const { isLoading, error } = useAppSelector((state) => state.auth);
  const logo = useAppSelector(selectLogo);
  const globalSetting = useAppSelector(selectGlobalSetting);
  const { storeCustomizationSetting } = useAppSelector((state) => state.setting);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert(t('Common.error'), t('Auth.pleaseFilAllFields'));
      return;
    }

    try {
      await dispatch(loginAsync({
        email: email.trim(),
        password,
      })).unwrap();
    } catch (error) {
      Alert.alert(t('Auth.loginFailed'), error as string);
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  useEffect(() => {
    // Charger les settings au démarrage pour récupérer le logo
    dispatch(fetchGlobalSettingsAsync());
    dispatch(fetchStoreCustomizationSettingsAsync());

    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={ELuxeColors.white} />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header avec Logo */}
          <View style={styles.header}>
            {/* Logo priorité: navbar.logo > logo > fallback */}
            {(storeCustomizationSetting?.navbar?.logo || logo) ? (
              <Image
                source={{ uri: storeCustomizationSetting?.navbar?.logo || logo }}
                style={styles.logo}
                resizeMode="contain"
                onError={() => console.log('Erreur chargement logo')}
              />
            ) : (
              <Text style={styles.logoText}>E-Luxe</Text>
            )}
            <Text style={styles.title}>{t('Auth.welcomeBack')}</Text>
            <Text style={styles.subtitle}>{t('Auth.signInToAccount')}</Text>
          </View>

          {/* Form simple sans carte */}
          <View style={styles.form}>
            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Icon name="error" size={20} color={ELuxeColors.error} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Email Input */}
            <View style={styles.inputGroup}>
              <View style={styles.inputContainer}>
                <Icon name="email" size={20} color="#666666" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder={t('Auth.enterEmail')}
                  placeholderTextColor="#999999"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Password Input */}
            <View style={styles.inputGroup}>
              <View style={styles.inputContainer}>
                <Icon name="lock" size={20} color="#666666" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder={t('Auth.enterPassword')}
                  placeholderTextColor="#999999"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Icon
                    name={showPassword ? 'visibility' : 'visibility-off'}
                    size={20}
                    color="#666666"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Remember Me & Forgot Password */}
            <View style={styles.optionsRow}>
              <TouchableOpacity
                style={styles.rememberMe}
                onPress={() => setRememberMe(!rememberMe)}
              >
                <Icon
                  name={rememberMe ? 'check-box' : 'check-box-outline-blank'}
                  size={20}
                  color={ELuxeColors.primary}
                />
                <Text style={styles.rememberMeText}>{t('Auth.rememberMe')}</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handleForgotPassword}>
                <Text style={styles.forgotPassword}>{t('Auth.forgotPassword')}</Text>
              </TouchableOpacity>
            </View>

            {/* Login Button */}
            <TouchableOpacity
              style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.loginButtonText}>{t('Auth.signIn')}</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Divider */}
          <View style={styles.dividerContainer}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>Or continue with</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* Social Login Buttons */}
          <View style={styles.socialButtonsContainer}>
            <TouchableOpacity style={styles.socialButton}>
              <View style={[styles.socialIcon, { backgroundColor: '#1877F2' }]}>
                <Text style={styles.socialIconText}>f</Text>
              </View>
              <Text style={styles.socialButtonText}>Continue with Facebook</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.socialButton}>
              <View style={[styles.socialIcon, { backgroundColor: '#DB4437' }]}>
                <Text style={styles.socialIconText}>G</Text>
              </View>
              <Text style={styles.socialButtonText}>Continue with Google</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.socialButton}>
              <View style={[styles.socialIcon, { backgroundColor: '#000000' }]}>
                <Text style={styles.socialIconText}></Text>
              </View>
              <Text style={styles.socialButtonText}>Continue with Apple</Text>
            </TouchableOpacity>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>{t('Auth.dontHaveAccount')} </Text>
            <TouchableOpacity onPress={handleRegister}>
              <Text style={styles.registerLink}>{t('Auth.signUp')}</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ELuxeColors.white,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 80,
    marginBottom: 24,
  },
  logoText: {
    fontSize: 36,
    fontWeight: '700',
    color: ELuxeColors.primary,
    marginBottom: 24,
    textAlign: 'center',
  },
  title: {
    fontSize: 28,
    color: '#000000',
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  form: {
    marginBottom: 24,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#FF0000',
    marginLeft: 8,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ELuxeColors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    backgroundColor: ELuxeColors.white,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#000000',
  },
  eyeIcon: {
    padding: 4,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  rememberMe: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rememberMeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666666',
  },
  forgotPassword: {
    fontSize: 14,
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
  loginButton: {
    backgroundColor: ELuxeColors.primary,
    borderRadius: 8,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  footerText: {
    fontSize: 16,
    color: '#666666',
  },
  registerLink: {
    fontSize: 16,
    color: ELuxeColors.primary,
    fontWeight: '600',
  },
  // Styles pour les boutons sociaux
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: ELuxeColors.border,
  },
  dividerText: {
    fontSize: 14,
    color: '#666666',
    marginHorizontal: 16,
  },
  socialButtonsContainer: {
    gap: 12,
    marginBottom: 24,
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    height: 50,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  socialIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  socialIconText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  socialButtonText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '500',
    flex: 1,
    textAlign: 'center',
  },
});

export default LoginScreen;
