import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Image,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { loginAsync, clearError } from '../../store/slices/authSlice';
import { AuthStackParamList } from '../../navigation/AppNavigator';
import { selectLogo, selectGlobalSetting } from '../../store/slices/settingsSlice';
import { ELuxeColors } from '../../../theme/colors';
import { TextStyles, FontWeights } from '../../../theme/typography';
import { useTranslation } from 'react-i18next';
import Logo from '../../components/common/Logo';

type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Login'>;

const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert(t('Common.error'), t('Auth.pleaseFilAllFields'));
      return;
    }

    try {
      await dispatch(loginAsync({
        email: email.trim(),
        password,
      })).unwrap();
    } catch (error) {
      Alert.alert(t('Auth.loginFailed'), error as string);
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  React.useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={ELuxeColors.primary} />
      <LinearGradient
        colors={[ELuxeColors.primary, ELuxeColors.secondary]}
        style={styles.container}
      >
        <KeyboardAvoidingView
          style={styles.keyboardContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView contentContainerStyle={styles.scrollContent}>
            {/* Header avec Logo */}
            <View style={styles.header}>
              <Logo
                size="large"
                variant="light"
                showText={true}
                style={styles.logoContainer}
              />
              <Text style={styles.title}>{t('Auth.welcomeBack')}</Text>
              <Text style={styles.subtitle}>{t('Auth.signInToAccount')}</Text>
            </View>

            {/* Form Card */}
            <View style={styles.formCard}>
              {/* Error Message */}
              {error && (
                <View style={styles.errorContainer}>
                  <Icon name="error" size={20} color={ELuxeColors.error} />
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              )}

              {/* Email Input */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>{t('Auth.email')}</Text>
                <View style={styles.inputContainer}>
                  <Icon name="email" size={20} color={ELuxeColors.textSecondary} style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder={t('Auth.enterEmail')}
                    placeholderTextColor={ELuxeColors.textSecondary}
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
              </View>

              {/* Password Input */}
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>{t('Auth.password')}</Text>
                <View style={styles.inputContainer}>
                  <Icon name="lock" size={20} color={ELuxeColors.textSecondary} style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder={t('Auth.enterPassword')}
                    placeholderTextColor={ELuxeColors.textSecondary}
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.eyeIcon}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Icon
                      name={showPassword ? 'visibility' : 'visibility-off'}
                      size={20}
                      color={ELuxeColors.textSecondary}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Remember Me & Forgot Password */}
              <View style={styles.optionsRow}>
                <TouchableOpacity
                  style={styles.rememberMe}
                  onPress={() => setRememberMe(!rememberMe)}
                >
                  <Icon
                    name={rememberMe ? 'check-box' : 'check-box-outline-blank'}
                    size={20}
                    color={ELuxeColors.primary}
                  />
                  <Text style={styles.rememberMeText}>{t('Auth.rememberMe')}</Text>
                </TouchableOpacity>

                <TouchableOpacity onPress={handleForgotPassword}>
                  <Text style={styles.forgotPassword}>{t('Auth.forgotPassword')}</Text>
                </TouchableOpacity>
              </View>

              {/* Login Button */}
              <TouchableOpacity
                style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
                onPress={handleLogin}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text style={styles.loginButtonText}>{t('Auth.signIn')}</Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>{t('Auth.dontHaveAccount')} </Text>
              <TouchableOpacity onPress={handleRegister}>
                <Text style={styles.registerLink}>{t('Auth.signUp')}</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 24,
  },
  title: {
    ...TextStyles.h3,
    color: ELuxeColors.white,
    fontWeight: FontWeights.semiBold,
    marginBottom: 8,
  },
  subtitle: {
    ...TextStyles.body,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  formCard: {
    backgroundColor: ELuxeColors.white,
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    ...TextStyles.caption,
    color: ELuxeColors.error,
    marginLeft: 8,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    ...TextStyles.caption,
    color: ELuxeColors.textPrimary,
    fontWeight: FontWeights.medium,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ELuxeColors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: ELuxeColors.background,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    height: 50,
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
  },
  eyeIcon: {
    padding: 4,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  rememberMe: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rememberMeText: {
    marginLeft: 8,
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
  },
  forgotPassword: {
    ...TextStyles.caption,
    color: ELuxeColors.primary,
    fontWeight: FontWeights.medium,
  },
  loginButton: {
    backgroundColor: ELuxeColors.primary,
    borderRadius: 12,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: ELuxeColors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
    fontWeight: FontWeights.semiBold,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  footerText: {
    ...TextStyles.body,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  registerLink: {
    ...TextStyles.body,
    color: ELuxeColors.white,
    fontWeight: FontWeights.semiBold,
  },
});

export default LoginScreen;
