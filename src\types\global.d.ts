/**
 * Déclarations TypeScript globales pour l'application E-Luxe Mobile
 * Ces déclarations permettent de définir des types globaux et de corriger
 * les problèmes de compatibilité TypeScript
 */

// Déclarations pour React Native
declare var __DEV__: boolean;

// Déclarations pour les modules sans types
declare module '*.png' {
  const value: any;
  export default value;
}

declare module '*.jpg' {
  const value: any;
  export default value;
}

declare module '*.jpeg' {
  const value: any;
  export default value;
}

declare module '*.gif' {
  const value: any;
  export default value;
}

declare module '*.svg' {
  const value: any;
  export default value;
}

// Déclarations pour les modules de navigation
declare module '@react-navigation/native' {
  export * from '@react-navigation/native/lib/typescript/src';
}

declare module '@react-navigation/stack' {
  export * from '@react-navigation/stack/lib/typescript/src';
}

declare module '@react-navigation/bottom-tabs' {
  export * from '@react-navigation/bottom-tabs/lib/typescript/src';
}

// Déclarations pour les icônes
declare module 'react-native-vector-icons/MaterialIcons' {
  import { Component } from 'react';
  import { TextProps } from 'react-native';
  
  interface IconProps extends TextProps {
    name: string;
    size?: number;
    color?: string;
  }
  
  export default class Icon extends Component<IconProps> {}
}

// Déclarations pour FastImage
declare module 'react-native-fast-image' {
  import { Component } from 'react';
  import { ImageProps, ImageResizeMode } from 'react-native';
  
  interface FastImageProps extends Omit<ImageProps, 'source'> {
    source: {
      uri: string;
      priority?: 'low' | 'normal' | 'high';
      cache?: 'immutable' | 'web' | 'cacheOnly';
    } | number;
    resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  }
  
  export default class FastImage extends Component<FastImageProps> {
    static resizeMode: {
      contain: 'contain';
      cover: 'cover';
      stretch: 'stretch';
      center: 'center';
    };
  }
}

// Types utilitaires globaux
declare global {
  type Nullable<T> = T | null;
  type Optional<T> = T | undefined;
  type Dictionary<T = any> = Record<string, T>;
  
  // Types pour les erreurs API
  interface ApiError {
    message: string;
    status?: number;
    code?: string;
  }
  
  // Types pour les réponses API
  interface ApiResponse<T = any> {
    data: T;
    message?: string;
    success: boolean;
    status: number;
  }
}

export {};
