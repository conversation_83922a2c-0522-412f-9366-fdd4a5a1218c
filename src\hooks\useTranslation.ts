/**
 * Hook de traduction pour Mobile E-Luxe 1.0
 * Version simplifiée sans dépendances i18n externes
 * Compatible avec l'architecture multilingue E-Luxe
 */

import { useState } from 'react';

// Traductions simplifiées (version de base)
const translations = {
  en: {
    Header: {
      title: 'E-Luxe',
      searchProducts: 'Search products...',
      cart: 'Cart',
      menu: 'Menu',
    },
    Navigation: {
      home: 'Home',
      categories: 'Categories',
      products: 'Products',
      cart: 'Cart',
      account: 'Account',
      settings: 'Settings',
      language: 'Language',
    },
    Slider: {
      title: 'Exclusive Collection',
      subtitle: 'Discover luxury products',
      buttonText: 'Shop Now',
    },
    Product: {
      seeMore: 'See More',
      addToCart: 'Add to Cart',
    },
    Common: {
      loading: 'Loading...',
      error: 'Error',
      ok: 'OK',
      cancel: 'Cancel',
    },
  },
  fr: {
    Header: {
      title: 'E-Luxe',
      searchProducts: 'Rechercher des produits...',
      cart: 'Panier',
      menu: 'Menu',
    },
    Navigation: {
      home: 'Accueil',
      categories: 'Catégories',
      products: 'Produits',
      cart: 'Panier',
      account: 'Compte',
      settings: 'Paramètres',
      language: 'Langue',
    },
    Slider: {
      title: 'Collection Exclusive',
      subtitle: 'Découvrez des produits de luxe',
      buttonText: 'Acheter maintenant',
    },
    Product: {
      seeMore: 'Voir plus',
      addToCart: 'Ajouter au panier',
    },
    Common: {
      loading: 'Chargement...',
      error: 'Erreur',
      ok: 'OK',
      cancel: 'Annuler',
    },
  },
};

// Langue par défaut
let currentLanguage = 'en';

/**
 * Hook principal de traduction (comme useTranslations du client web)
 */
export const useTranslations = (namespace?: string) => {
  const [language] = useState(currentLanguage);
  
  // Fonction de traduction
  const translate = (key: string) => {
    try {
      const fullKey = namespace ? `${namespace}.${key}` : key;
      const keys = fullKey.split('.');
      
      let value: any = translations[language as keyof typeof translations];
      for (const k of keys) {
        value = value?.[k];
      }
      
      return typeof value === 'string' ? value : key;
    } catch (error) {
      console.log('Erreur traduction:', key, error);
      return key;
    }
  };
  
  return translate;
};

/**
 * Hook pour la gestion des langues
 */
export const useLanguage = () => {
  const [language, setLanguage] = useState(currentLanguage);
  
  // Fonction pour changer de langue
  const changeLanguageHandler = (newLanguage: string) => {
    if (['en', 'fr'].includes(newLanguage)) {
      currentLanguage = newLanguage;
      setLanguage(newLanguage);
      console.log('🌍 Langue changée vers:', newLanguage);
    }
  };
  
  return {
    currentLanguage: language,
    changeLanguage: changeLanguageHandler,
    supportedLanguages: ['en', 'fr'],
    isLanguageSupported: (lang: string) => ['en', 'fr'].includes(lang),
    getLanguageName: (lang: string) => {
      const names: Record<string, string> = {
        en: 'English',
        fr: 'Français',
      };
      return names[lang] || lang;
    },
  };
};

/**
 * Hook pour les utilitaires de traduction
 */
export const useTranslationUtils = () => {
  const { currentLanguage } = useLanguage();
  
  return {
    // Fonction showingTranslateValue (comme le client web)
    showingTranslateValue: (value: any, lang?: string): string => {
      const targetLang = lang || currentLanguage;
      
      if (typeof value === 'string') {
        return value;
      }

      if (!value) {
        return '';
      }

      if (typeof value === 'object' && value !== null) {
        if (value[targetLang]) {
          return value[targetLang];
        }
        if (value['en']) {
          return value['en'];
        }
        if (value['fr']) {
          return value['fr'];
        }
        const keys = Object.keys(value);
        if (keys.length > 0) {
          return value[keys[0]];
        }
      }

      return String(value);
    },
    
    currentLanguage,
    
    formatPriceWithCurrency: (price: number, currency: string = 'USD'): string => {
      if (!price || isNaN(price)) {
        return '0.00';
      }
      return new Intl.NumberFormat(currentLanguage === 'fr' ? 'fr-FR' : 'en-US', {
        style: 'currency',
        currency: currency,
      }).format(price);
    },
  };
};

/**
 * Hook pour la locale
 */
export const useLocale = () => {
  const { currentLanguage } = useLanguage();
  return currentLanguage as 'en' | 'fr';
};

// Export des types pour TypeScript
export type Locale = 'en' | 'fr';
export type TranslationFunction = (key: string) => string;
