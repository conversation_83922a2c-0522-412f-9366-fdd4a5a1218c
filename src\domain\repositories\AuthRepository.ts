import { AuthUser, LoginCredentials, RegisterData, ResetPasswordData, ChangePasswordData, User } from '../entities/User';

export interface AuthRepository {
  // Authentication
  login(credentials: LoginCredentials): Promise<AuthUser>;
  register(data: RegisterData): Promise<AuthUser>;
  logout(): Promise<void>;
  refreshToken(refreshToken: string): Promise<AuthUser>;
  
  // Password Management
  forgotPassword(data: ResetPasswordData): Promise<void>;
  resetPassword(token: string, newPassword: string): Promise<void>;
  changePassword(data: ChangePasswordData): Promise<void>;
  
  // User Profile
  getCurrentUser(): Promise<User>;
  updateProfile(userData: Partial<User>): Promise<User>;
  uploadAvatar(imageUri: string): Promise<string>;
  
  // Token Management
  getStoredToken(): Promise<string | null>;
  getStoredUser(): Promise<User | null>;
  storeToken(token: string): Promise<void>;
  removeToken(): Promise<void>;
  
  // Session Management
  isAuthenticated(): Promise<boolean>;
  validateToken(token: string): Promise<boolean>;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}
