/**
 * ProductSlice PROPRE - Mobile E-Luxe 1.0
 * EXACTEMENT comme le client web - AUCUNE donnée mockée
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ecommerceService } from '../../../services/EcommerceApiService';

// State interface SIMPLE et PROPRE
interface ProductState {
  // Produits
  products: any[];
  currentProduct: any | null;
  bestSellingProducts: any[];
  showingProducts: any[];
  
  // Catégories
  categories: any[];
  showingCategories: any[];
  
  // Pagination
  pagination: {
    page: number;
    totalPages: number;
    total: number;
    limit: number;
  };
  
  // États
  isLoading: boolean;
  error: string | null;
  
  // Filtres
  filter: {
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    page?: number;
    limit?: number;
  };
}

const initialState: ProductState = {
  products: [],
  currentProduct: null,
  bestSellingProducts: [],
  showingProducts: [],
  categories: [],
  showingCategories: [],
  pagination: {
    page: 1,
    totalPages: 1,
    total: 0,
    limit: 48,
  },
  isLoading: false,
  error: null,
  filter: {},
};

// ===== ACTIONS ASYNC PROPRES =====

/**
 * Récupère les produits avec pagination et filtres
 */
export const getProductsAsync = createAsyncThunk(
  'product/getProducts',
  async (params: any = {}, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getProducts(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch products');
    }
  }
);

/**
 * Récupère un produit par ID - EXACTEMENT comme le client web
 */
export const getProductByIdAsync = createAsyncThunk(
  'product/getProductById',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getProductById(productId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch product');
    }
  }
);

/**
 * Récupère les meilleurs vendeurs
 */
export const getBestSellersAsync = createAsyncThunk(
  'product/getBestSellers',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getBestSellersOfMonth();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch best sellers');
    }
  }
);

/**
 * Récupère les produits affichés
 */
export const getShowingProductsAsync = createAsyncThunk(
  'product/getShowingProducts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getFeaturedProducts();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch showing products');
    }
  }
);

/**
 * Récupère les catégories
 */
export const getCategoriesAsync = createAsyncThunk(
  'product/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch categories');
    }
  }
);

/**
 * Récupère les catégories affichées - EXACTEMENT comme le client web
 */
export const getShowingCategoriesAsync = createAsyncThunk(
  'product/getShowingCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getShowingCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch showing categories');
    }
  }
);

/**
 * Récupère les meilleurs vendeurs du mois - EXACTEMENT comme le client web
 */
export const getBestSellersOfMonthAsync = createAsyncThunk(
  'product/getBestSellersOfMonth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getBestSellersOfMonth();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch best sellers of month');
    }
  }
);

/**
 * Récupère les catégories avec produits pour la page d'accueil - EXACTEMENT comme le client web
 */
export const getShowingProductsOnHomePageCategoriesAsync = createAsyncThunk(
  'product/getShowingProductsOnHomePageCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getShowingProductsOnHomePageCategories();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch homepage categories');
    }
  }
);

/**
 * Récupère les offres spéciales
 */
export const getDealsAsync = createAsyncThunk(
  'product/getDeals',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ecommerceService.getProducts({ search: 'deals' });
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch deals');
    }
  }
);

// ===== SLICE PROPRE =====

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<any>) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    clearFilter: (state) => {
      state.filter = {};
    },
    clearProducts: (state) => {
      state.products = [];
      state.pagination = initialState.pagination;
    },
    setCurrentProduct: (state, action: PayloadAction<any>) => {
      state.currentProduct = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Get products
    builder
      .addCase(getProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        const data = action.payload.data || action.payload;
        state.products = data.products || data;
        state.pagination = {
          page: data.page || 1,
          totalPages: data.totalPages || 1,
          total: data.total || 0,
          limit: data.limit || 48,
        };
      })
      .addCase(getProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get product by ID
    builder
      .addCase(getProductByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getProductByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProduct = action.payload.data || action.payload;
      })
      .addCase(getProductByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get best sellers
    builder
      .addCase(getBestSellersAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBestSellersAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bestSellingProducts = action.payload.data || action.payload;
      })
      .addCase(getBestSellersAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get showing products
    builder
      .addCase(getShowingProductsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getShowingProductsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.showingProducts = action.payload.data || action.payload;
      })
      .addCase(getShowingProductsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get categories
    builder
      .addCase(getCategoriesAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCategoriesAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.categories = action.payload.data || action.payload;
      })
      .addCase(getCategoriesAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get showing categories
    builder
      .addCase(getShowingCategoriesAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getShowingCategoriesAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.showingCategories = action.payload.data || action.payload;
      })
      .addCase(getShowingCategoriesAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get best sellers of month
    builder
      .addCase(getBestSellersOfMonthAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getBestSellersOfMonthAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.bestSellingProducts = action.payload.data || action.payload;
      })
      .addCase(getBestSellersOfMonthAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

    // Get showing products on homepage categories
    builder
      .addCase(getShowingProductsOnHomePageCategoriesAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getShowingProductsOnHomePageCategoriesAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.showingCategories = action.payload.data || action.payload;
      })
      .addCase(getShowingProductsOnHomePageCategoriesAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setFilter, clearFilter, clearProducts, setCurrentProduct } = productSlice.actions;
export default productSlice.reducer;
