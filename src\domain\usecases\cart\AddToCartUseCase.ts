import { OrderRepository } from '../../repositories/OrderRepository';
import { ProductRepository } from '../../repositories/ProductRepository';
import { Cart, CartItem } from '../../entities/Order';

export interface AddToCartParams {
  userId: string;
  productId: string;
  quantity: number;
  variantId?: string;
  extraIds?: string[];
}

export class AddToCartUseCase {
  constructor(
    private orderRepository: OrderRepository,
    private productRepository: ProductRepository
  ) {}

  async execute(params: AddToCartParams): Promise<Cart> {
    try {
      // Validate input
      this.validateParams(params);

      // Get product details
      const product = await this.productRepository.getProductById(params.productId);
      
      if (!product) {
        throw new Error('Product not found');
      }

      if (product.status === 'hide') {
        throw new Error('Product is not available');
      }

      // Check stock availability
      const availableQuantity = this.getAvailableQuantity(product, params.variantId);
      if (availableQuantity < params.quantity) {
        throw new Error(`Only ${availableQuantity} items available in stock`);
      }

      // Calculate price
      const price = this.calculatePrice(product, params.variantId);

      // Create cart item
      const cartItem: Omit<CartItem, 'id' | 'total'> = {
        productId: params.productId,
        title: this.getProductTitle(product),
        image: product.image,
        price: price,
        quantity: params.quantity,
        variant: params.variantId ? this.getVariantDetails(product, params.variantId) : undefined,
        extras: params.extraIds ? await this.getExtrasDetails(params.extraIds) : undefined,
      };

      // Add to cart
      const updatedCart = await this.orderRepository.addToCart(params.userId, cartItem);

      return updatedCart;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to add to cart: ${error.message}`);
      }
      throw new Error('Failed to add to cart: Unknown error');
    }
  }

  private validateParams(params: AddToCartParams): void {
    if (!params.userId) {
      throw new Error('User ID is required');
    }

    if (!params.productId) {
      throw new Error('Product ID is required');
    }

    if (!params.quantity || params.quantity < 1) {
      throw new Error('Quantity must be at least 1');
    }

    if (params.quantity > 99) {
      throw new Error('Maximum quantity is 99');
    }
  }

  private getAvailableQuantity(product: any, variantId?: string): number {
    if (variantId && product.variants) {
      const variant = product.variants.find((v: any) => v.id === variantId);
      return variant ? variant.quantity : 0;
    }
    return product.quantity;
  }

  private calculatePrice(product: any, variantId?: string): number {
    if (variantId && product.variants) {
      const variant = product.variants.find((v: any) => v.id === variantId);
      return variant ? (variant.sale_price || variant.price) : product.price;
    }
    return product.sale_price || product.price;
  }

  private getProductTitle(product: any): string {
    if (typeof product.title === 'object') {
      return product.title.en || product.title.fr || Object.values(product.title)[0] as string;
    }
    return product.title;
  }

  private getVariantDetails(product: any, variantId: string): any {
    const variant = product.variants.find((v: any) => v.id === variantId);
    if (!variant) return undefined;

    return {
      id: variant.id,
      attributes: variant.attributes.reduce((acc: any, attr: any) => {
        acc[attr.name] = attr.value;
        return acc;
      }, {}),
      price: variant.sale_price || variant.price,
      sku: variant.sku,
    };
  }

  private async getExtrasDetails(extraIds: string[]): Promise<any[]> {
    // This would typically fetch extra details from a repository
    // For now, return empty array
    return [];
  }
}
