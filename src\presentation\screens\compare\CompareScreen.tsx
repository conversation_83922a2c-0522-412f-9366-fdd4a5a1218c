/**
 * CompareScreen - Mobile E-Luxe 1.0
 * Page de comparaison de produits reproduisant le contenu du client web
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { ELuxeColors, ComponentColors } from '../../../theme/colors';
import { TextStyles } from '../../../theme/typography';
import Icon from 'react-native-vector-icons/MaterialIcons';

type CompareScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const CompareScreen: React.FC = () => {
  const navigation = useNavigation<CompareScreenNavigationProp>();
  const dispatch = useAppDispatch();
  
  // TODO: Get compare products from Redux state
  const [compareProducts, setCompareProducts] = useState([]);

  const removeProduct = (productId: string) => {
    Alert.alert(
      'Remove Product',
      'Are you sure you want to remove this product from comparison?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setCompareProducts(prev => prev.filter(p => p.id !== productId));
          },
        },
      ]
    );
  };

  const addToCart = (product: any) => {
    // TODO: Implement add to cart
    Alert.alert('Added to Cart', `${product.name} has been added to your cart.`);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Icon key={i} name="star" size={16} color="#FFD700" />);
    }

    if (hasHalfStar) {
      stars.push(<Icon key="half" name="star-half" size={16} color="#FFD700" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Icon key={`empty-${i}`} name="star-border" size={16} color="#FFD700" />);
    }

    return stars;
  };

  const renderComparisonRow = (label: string, values: string[]) => (
    <View style={styles.comparisonRow}>
      <View style={styles.labelColumn}>
        <Text style={styles.labelText}>{label}</Text>
      </View>
      {values.map((value, index) => (
        <View key={index} style={styles.valueColumn}>
          <Text style={styles.valueText}>{value}</Text>
        </View>
      ))}
    </View>
  );

  if (compareProducts.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyState}>
          <Icon name="compare" size={64} color={ELuxeColors.textTertiary} />
          <Text style={styles.emptyTitle}>No Products to Compare</Text>
          <Text style={styles.emptyDescription}>
            Add products to your comparison list to see them here
          </Text>
          <TouchableOpacity 
            style={styles.shopButton}
            onPress={() => navigation.navigate('Shop')}
          >
            <Text style={styles.shopButtonText}>Start Shopping</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.compareTable}>
          {/* Product Images Row */}
          <View style={styles.productRow}>
            <View style={styles.labelColumn}>
              <Text style={styles.sectionTitle}>Products</Text>
            </View>
            {compareProducts.map((product) => (
              <View key={product.id} style={styles.productColumn}>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeProduct(product.id)}
                >
                  <Icon name="close" size={20} color={ELuxeColors.textSecondary} />
                </TouchableOpacity>
                <Image source={{ uri: product.image }} style={styles.productImage} />
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productBrand}>{product.brand}</Text>
              </View>
            ))}
          </View>

          {/* Price Row */}
          <View style={styles.comparisonRow}>
            <View style={styles.labelColumn}>
              <Text style={styles.labelText}>Price</Text>
            </View>
            {compareProducts.map((product) => (
              <View key={product.id} style={styles.valueColumn}>
                <Text style={styles.priceText}>${product.price}</Text>
                {product.originalPrice && product.originalPrice > product.price && (
                  <Text style={styles.originalPriceText}>${product.originalPrice}</Text>
                )}
              </View>
            ))}
          </View>

          {/* Rating Row */}
          <View style={styles.comparisonRow}>
            <View style={styles.labelColumn}>
              <Text style={styles.labelText}>Rating</Text>
            </View>
            {compareProducts.map((product) => (
              <View key={product.id} style={styles.valueColumn}>
                <View style={styles.ratingContainer}>
                  <View style={styles.stars}>{renderStars(product.rating)}</View>
                  <Text style={styles.ratingText}>{product.rating}</Text>
                </View>
              </View>
            ))}
          </View>

          {/* Features Section */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Features</Text>
          </View>

          {/* Features Rows */}
          {compareProducts[0]?.features.map((_, featureIndex) => (
            <View key={featureIndex} style={styles.comparisonRow}>
              <View style={styles.labelColumn}>
                <Text style={styles.labelText}>Feature {featureIndex + 1}</Text>
              </View>
              {compareProducts.map((product) => (
                <View key={product.id} style={styles.valueColumn}>
                  <Text style={styles.valueText}>
                    {product.features[featureIndex] || '-'}
                  </Text>
                </View>
              ))}
            </View>
          ))}

          {/* Specifications Section */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Specifications</Text>
          </View>

          {/* Specification Rows */}
          {Object.keys(compareProducts[0]?.specifications || {}).map((specKey) => (
            <View key={specKey} style={styles.comparisonRow}>
              <View style={styles.labelColumn}>
                <Text style={styles.labelText}>{specKey}</Text>
              </View>
              {compareProducts.map((product) => (
                <View key={product.id} style={styles.valueColumn}>
                  <Text style={styles.valueText}>
                    {product.specifications[specKey] || '-'}
                  </Text>
                </View>
              ))}
            </View>
          ))}

          {/* Action Buttons Row */}
          <View style={styles.actionRow}>
            <View style={styles.labelColumn}>
              <Text style={styles.labelText}>Actions</Text>
            </View>
            {compareProducts.map((product) => (
              <View key={product.id} style={styles.valueColumn}>
                <TouchableOpacity
                  style={styles.addToCartButton}
                  onPress={() => addToCart(product)}
                >
                  <Text style={styles.addToCartText}>Add to Cart</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.viewDetailsButton}
                  onPress={() => navigation.navigate('ProductDetail', { productId: product.id })}
                >
                  <Text style={styles.viewDetailsText}>View Details</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Add More Products Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.addMoreButton}
          onPress={() => navigation.navigate('Shop')}
        >
          <Icon name="add" size={20} color={ELuxeColors.primary} />
          <Text style={styles.addMoreText}>Add More Products</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ComponentColors.screen.background,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    ...TextStyles.h2,
    color: ELuxeColors.textPrimary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  shopButton: {
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  shopButtonText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
  },
  compareTable: {
    minWidth: 600,
    backgroundColor: ELuxeColors.white,
  },
  productRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border1,
    paddingVertical: 16,
  },
  labelColumn: {
    width: 120,
    paddingHorizontal: 12,
    justifyContent: 'center',
    backgroundColor: ELuxeColors.grey2,
  },
  productColumn: {
    width: 200,
    paddingHorizontal: 12,
    alignItems: 'center',
    position: 'relative',
  },
  removeButton: {
    position: 'absolute',
    top: 0,
    right: 8,
    zIndex: 1,
    padding: 4,
  },
  productImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  productName: {
    ...TextStyles.h5,
    color: ELuxeColors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  productBrand: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
  },
  comparisonRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: ELuxeColors.border2,
    minHeight: 50,
  },
  valueColumn: {
    width: 200,
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelText: {
    ...TextStyles.body,
    color: ELuxeColors.textPrimary,
    fontWeight: '600',
  },
  valueText: {
    ...TextStyles.body,
    color: ELuxeColors.textSecondary,
    textAlign: 'center',
  },
  priceText: {
    ...TextStyles.h4,
    color: ELuxeColors.primary,
    fontWeight: 'bold',
  },
  originalPriceText: {
    ...TextStyles.caption,
    color: ELuxeColors.textTertiary,
    textDecorationLine: 'line-through',
  },
  ratingContainer: {
    alignItems: 'center',
    gap: 4,
  },
  stars: {
    flexDirection: 'row',
  },
  ratingText: {
    ...TextStyles.caption,
    color: ELuxeColors.textSecondary,
  },
  sectionHeader: {
    backgroundColor: ELuxeColors.primary,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  sectionTitle: {
    ...TextStyles.h4,
    color: ELuxeColors.white,
    fontWeight: 'bold',
  },
  actionRow: {
    flexDirection: 'row',
    paddingVertical: 16,
  },
  addToCartButton: {
    backgroundColor: ELuxeColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginBottom: 8,
  },
  addToCartText: {
    ...TextStyles.button,
    color: ELuxeColors.white,
    textAlign: 'center',
  },
  viewDetailsButton: {
    borderWidth: 1,
    borderColor: ELuxeColors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  viewDetailsText: {
    ...TextStyles.button,
    color: ELuxeColors.primary,
    textAlign: 'center',
  },
  footer: {
    backgroundColor: ELuxeColors.white,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: ELuxeColors.border1,
  },
  addMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: ELuxeColors.primary,
    borderStyle: 'dashed',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  addMoreText: {
    ...TextStyles.button,
    color: ELuxeColors.primary,
  },
});

export default CompareScreen;
